<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    header("location: ../index.php");
    exit;
}

require_once 'config.php';
require_once 'functions.php';

$success_message = "";
$error_message = "";

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['update_profile'])) {
        $new_username = trim($_POST['username']);
        $new_email = trim($_POST['email']);
        $new_first_name = trim($_POST['first_name']);
        $new_last_name = trim($_POST['last_name']);
        $new_phone = trim($_POST['phone_number']);
        
        // Validate inputs
        if (empty($new_username)) {
            $error_message = "Username is required.";
        } elseif (empty($new_email)) {
            $error_message = "Email is required.";
        } else {
            // Check if username is already taken by another user
            $check_sql = "SELECT id FROM users WHERE username = ? AND id != ?";
            if ($check_stmt = mysqli_prepare($link, $check_sql)) {
                mysqli_stmt_bind_param($check_stmt, "si", $new_username, $_SESSION["id"]);
                mysqli_stmt_execute($check_stmt);
                mysqli_stmt_store_result($check_stmt);
                
                if (mysqli_stmt_num_rows($check_stmt) > 0) {
                    $error_message = "Username is already taken.";
                } else {
                    // Update profile
                    $update_sql = "UPDATE users SET username = ?, email = ?, first_name = ?, last_name = ?, phone_number = ? WHERE id = ?";
                    if ($update_stmt = mysqli_prepare($link, $update_sql)) {
                        mysqli_stmt_bind_param($update_stmt, "sssssi", $new_username, $new_email, $new_first_name, $new_last_name, $new_phone, $_SESSION["id"]);
                        
                        if (mysqli_stmt_execute($update_stmt)) {
                            $_SESSION["username"] = $new_username;
                            $success_message = "Profile updated successfully!";
                        } else {
                            $error_message = "Something went wrong. Please try again later.";
                        }
                        mysqli_stmt_close($update_stmt);
                    }
                }
                mysqli_stmt_close($check_stmt);
            }
        }
    } elseif (isset($_POST['change_password'])) {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error_message = "All password fields are required.";
        } elseif ($new_password !== $confirm_password) {
            $error_message = "New passwords do not match.";
        } elseif (strlen($new_password) < 6) {
            $error_message = "New password must be at least 6 characters long.";
        } else {
            // Verify current password
            $verify_sql = "SELECT password FROM users WHERE id = ?";
            if ($verify_stmt = mysqli_prepare($link, $verify_sql)) {
                mysqli_stmt_bind_param($verify_stmt, "i", $_SESSION["id"]);
                mysqli_stmt_execute($verify_stmt);
                mysqli_stmt_bind_result($verify_stmt, $hashed_password);
                mysqli_stmt_fetch($verify_stmt);
                mysqli_stmt_close($verify_stmt);
                
                if (password_verify($current_password, $hashed_password)) {
                    // Update password
                    $new_hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $update_password_sql = "UPDATE users SET password = ? WHERE id = ?";
                    if ($update_password_stmt = mysqli_prepare($link, $update_password_sql)) {
                        mysqli_stmt_bind_param($update_password_stmt, "si", $new_hashed_password, $_SESSION["id"]);
                        
                        if (mysqli_stmt_execute($update_password_stmt)) {
                            $success_message = "Password changed successfully!";
                        } else {
                            $error_message = "Something went wrong. Please try again later.";
                        }
                        mysqli_stmt_close($update_password_stmt);
                    }
                } else {
                    $error_message = "Current password is incorrect.";
                }
            }
        }
    }
}

// Get current user data
$user_data = [
    'username' => '',
    'email' => '',
    'first_name' => '',
    'last_name' => '',
    'phone_number' => '',
    'created_at' => ''
];

$get_user_sql = "SELECT username, email, first_name, last_name, phone_number, created_at FROM users WHERE id = ?";
if ($get_user_stmt = mysqli_prepare($link, $get_user_sql)) {
    mysqli_stmt_bind_param($get_user_stmt, "i", $_SESSION["id"]);
    if (mysqli_stmt_execute($get_user_stmt)) {
        mysqli_stmt_bind_result($get_user_stmt, $username, $email, $first_name, $last_name, $phone_number, $created_at);
        if (mysqli_stmt_fetch($get_user_stmt)) {
            $user_data = [
                'username' => $username ?? '',
                'email' => $email ?? '',
                'first_name' => $first_name ?? '',
                'last_name' => $last_name ?? '',
                'phone_number' => $phone_number ?? '',
                'created_at' => $created_at ?? ''
            ];
        }
    }
    mysqli_stmt_close($get_user_stmt);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Profile - KelvinKMS.com</title>
    <link rel="stylesheet" href="../CSS/custom-modal.css" />
    <style>
        body { font-family: Arial, sans-serif; background-color: #a48f19; color: white; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: auto; background-color: rgb(5 195 182); padding: 10px; border-radius: 10px; box-shadow: 0 2px 8px rgb(0 0 0); }
        h1 { color: #ffffff; text-align: center; font-size: 26px; }
        
        /* Back to Admin button */
        .back-admin-btn {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #00bcaa;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .back-admin-btn:hover {
            background-color: #ffbf00ff;
            border-color: rgba(255, 255, 255, 0.3);
        }
        
        /* Profile sections */
        .profile-section {
            background-color: rgba(255, 255, 255, 0.1);
            border-left: 5px solid rgb(253, 202, 0);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .profile-section h3 { margin-top: 0; color: #ffffff; font-weight: bold; }
        
        /* Forms */
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: #ffffff; font-weight: bold; }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%; padding: 10px; border-radius: 8px; border: 2px solid #555;
            background-color: #333; color: white; font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            border-color: rgb(253, 202, 0);
            outline: none;
        }
        
        /* Buttons */
        .btn {
            padding: 10px 20px;
            border: 2px solid;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: rgb(253, 202, 0);
            color: white;
            border-color: rgb(253, 202, 0);
        }
        .btn-primary:hover {
            background-color: #ffbf00ff;
            border-color: rgba(255, 255, 255, 0.3);
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        
        /* Messages */
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: bold;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* Info display */
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
            color: #ffffff;
        }
        .info-value {
            color: #ccc;
        }
    </style>
</head>
<body>
    <!-- Back to Admin button -->
    <a href="admin.php" class="back-admin-btn">← Back to Admin Panel</a>

    <div class="container">
        <h1>👤 Admin Profile Management</h1>
        
        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success"><?= htmlspecialchars($success_message) ?></div>
        <?php endif; ?>
        
        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger"><?= htmlspecialchars($error_message) ?></div>
        <?php endif; ?>
        
        <!-- Account Information -->
        <div class="profile-section">
            <h3>📋 Account Information</h3>
            <div class="info-item">
                <span class="info-label">Account Created:</span>
                <span class="info-value"><?= $user_data['created_at'] ? date('Y-m-d H:i:s', strtotime($user_data['created_at'])) : 'Unknown' ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Admin Status:</span>
                <span class="info-value" style="color: #32cd32;">Active Administrator</span>
            </div>
        </div>
        
        <!-- Profile Update Form -->
        <div class="profile-section">
            <h3>✏️ Update Profile Information</h3>
            <form method="post" action="">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" value="<?= htmlspecialchars($user_data['username'] ?? '') ?>" required>
                </div>

                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($user_data['email'] ?? '') ?>" required>
                </div>

                <div class="form-group">
                    <label for="first_name">First Name</label>
                    <input type="text" id="first_name" name="first_name" value="<?= htmlspecialchars($user_data['first_name'] ?? '') ?>">
                </div>

                <div class="form-group">
                    <label for="last_name">Last Name</label>
                    <input type="text" id="last_name" name="last_name" value="<?= htmlspecialchars($user_data['last_name'] ?? '') ?>">
                </div>

                <div class="form-group">
                    <label for="phone_number">Phone Number</label>
                    <input type="text" id="phone_number" name="phone_number" value="<?= htmlspecialchars($user_data['phone_number'] ?? '') ?>">
                </div>
                
                <button type="submit" name="update_profile" class="btn btn-primary">💾 Update Profile</button>
            </form>
        </div>
        
        <!-- Password Change Form -->
        <div class="profile-section">
            <h3>🔒 Change Password</h3>
            <form method="post" action="">
                <div class="form-group">
                    <label for="current_password">Current Password</label>
                    <input type="password" id="current_password" name="current_password" required>
                </div>
                
                <div class="form-group">
                    <label for="new_password">New Password</label>
                    <input type="password" id="new_password" name="new_password" required>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">Confirm New Password</label>
                    <input type="password" id="confirm_password" name="confirm_password" required>
                </div>
                
                <button type="submit" name="change_password" class="btn btn-danger">🔑 Change Password</button>
            </form>
        </div>
    </div>

    <script src="../../../../KMS_Apps/KMS_Index/KMS_Authentication/KMS_JS/KMS_custom-modal.js"></script>
</body>
</html>
