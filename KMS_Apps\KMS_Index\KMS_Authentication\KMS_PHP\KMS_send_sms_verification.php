<?php
session_start();
header('Content-Type: application/json');
$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';

// Load Twilio SDK
if (!file_exists(__DIR__ . '/../vendor/autoload.php')) {
    echo json_encode([
        'success' => false,
        'message' => 'SMS service is not available. Please contact administrator.'
    ]);
    exit;
}

require __DIR__ . '/../vendor/autoload.php';

// Check if Twilio class exists
if (!class_exists('Twilio\Rest\Client')) {
    echo json_encode([
        'success' => false,
        'message' => 'SMS service is not properly configured. Please contact administrator.'
    ]);
    exit;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $phone_number = trim($_POST['phone_number']);
    
    if (empty($phone_number)) {
        echo json_encode([
            'success' => false,
            'message' => 'Phone number is required.'
        ]);
        exit;
    }
    
    // Validate and format phone number for US numbers
    $phone_number = preg_replace('/[^\d+]/', '', $phone_number);

    // Add + prefix if missing
    if (substr($phone_number, 0, 1) !== '+') {
        $phone_number = '+' . $phone_number;
    }

    // Validate US phone number format (+1 followed by 10 digits)
    if (!preg_match('/^\+1[2-9]\d{2}[2-9]\d{2}\d{4}$/', $phone_number)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid US phone number format. Please enter a valid 10-digit US phone number.'
        ]);
        exit;
    }
    
    // Generate a 6-digit verification code
    $verification_code = sprintf("%06d", mt_rand(1, 999999));
    
    // Store the verification code in session
    $_SESSION['sms_verification_code'] = $verification_code;
    $_SESSION['sms_phone_number'] = $phone_number;
    $_SESSION['sms_code_time'] = time();
    
    // --- Twilio SMS Sending Logic ---
    $sid = TWILIO_ACCOUNT_SID;
    $token = TWILIO_AUTH_TOKEN;
    $twilio_phone_number = TWILIO_PHONE_NUMBER;
    
    // Validate Twilio credentials
    if (strlen($sid) < 30) {
        echo json_encode([
            'success' => false,
            'message' => 'SMS service configuration error: Invalid Account SID.'
        ]);
        exit;
    }
    
    if (strlen($token) < 30) {
        echo json_encode([
            'success' => false,
            'message' => 'SMS service configuration error: Invalid Auth Token.'
        ]);
        exit;
    }
    
    if (substr($twilio_phone_number, 0, 1) !== '+') {
        echo json_encode([
            'success' => false,
            'message' => 'SMS service configuration error: Invalid phone number format.'
        ]);
        exit;
    }

    // Send SMS using Twilio
    try {
        $client = new \Twilio\Rest\Client($sid, $token);
        
        $message = $client->messages->create(
            $phone_number,
            [
                'from' => $twilio_phone_number,
                'body' => "Your KelvinKMS.com verification code is: {$verification_code}"
            ]
        );

        echo json_encode([
            'success' => true,
            'message' => 'Verification code sent successfully to ' . $phone_number
        ]);

    } catch (\Twilio\Exceptions\RestException $e) {
        error_log('Twilio SMS Error: ' . $e->getMessage());
        
        // Return specific error message based on Twilio error code
        $error_message = 'Failed to send SMS: ';
        switch ($e->getStatusCode()) {
            case 400:
                $error_message .= 'Invalid phone number format.';
                break;
            case 401:
                $error_message .= 'Invalid Twilio credentials.';
                break;
            case 403:
                $error_message .= 'Insufficient permissions or trial account limitations.';
                break;
            case 429:
                $error_message .= 'Rate limit exceeded. Please try again later.';
                break;
            default:
                $error_message .= 'Service temporarily unavailable. Please try again later.';
        }
        
        echo json_encode([
            'success' => false,
            'message' => $error_message
        ]);
        
    } catch (Exception $e) {
        error_log('General SMS Error: ' . $e->getMessage());
        
        echo json_encode([
            'success' => false,
            'message' => 'Failed to send SMS. Please try again later.'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>
