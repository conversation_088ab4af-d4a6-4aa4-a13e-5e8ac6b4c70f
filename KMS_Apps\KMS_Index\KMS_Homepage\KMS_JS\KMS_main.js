// Main JavaScript for KelvinKMS.com

// Navigation and slideshow
function scrollToSection(id) {
    const el = document.getElementById(id);
    if (el) el.scrollIntoView({ behavior: 'smooth' });
}

let slideIndex = 0;
showSlides();

function showSlides() {
    const slides = document.getElementsByClassName('slide');
    if (slides.length) {
        Array.from(slides).forEach(slide => slide.style.display = 'none');
        slideIndex = (slideIndex % slides.length) + 1;
        slides[slideIndex - 1].style.display = 'block';
        setTimeout(showSlides, 3000);
    }
}

// Page view counter and modal handling
document.addEventListener('DOMContentLoaded', function() {
    // Use a more robust approach with multiple fallbacks for Chrome compatibility
    function updatePageViewCounter() {
        fetch('KMS_Apps/KMS_Index/KMS_UserTracking/KMS_PHP/KMS_get_view_count.php')
            .then(res => {
                if (!res.ok) {
                    throw new Error('HTTP error! status: ' + res.status);
                }
                return res.json();
            })
            .then(data => {
                const c = document.getElementById('counter');
                if (c && data && data.count !== undefined) {
                    c.innerText = data.count;
                    console.log('Page views updated to:', data.count);
                } else {
                    console.error('Counter element not found or invalid data:', data);
                }
            })
            .catch(error => {
                console.error('Page view counter error:', error);
                const c = document.getElementById('counter');
                if (c) c.innerText = '0';
            });
    }

    // Try immediate update
    updatePageViewCounter();
    
    // Also try after a short delay to ensure DOM is fully ready (Chrome fallback)
    setTimeout(updatePageViewCounter, 100);

    const registerModal = document.getElementById('registerModal');
    const loginModal = document.getElementById('loginModal');
    const registerBtn = document.getElementById('registerBtn');
    const loginBtn = document.getElementById('loginBtn');
    const closeRegister = document.getElementById('closeRegister');
    const closeLogin = document.getElementById('closeLogin');
    const registerCloseBtn = document.getElementById('registerCloseBtn');
    const loginCloseBtn = document.getElementById('loginCloseBtn');

    // Function to disable background scrolling
    function disableBackgroundScroll() {
        document.body.style.overflow = 'hidden';
    }

    // Function to enable background scrolling
    function enableBackgroundScroll() {
        document.body.style.overflow = 'auto';
    }

    // Register modal handlers
    if (registerBtn && registerModal) {
        registerBtn.onclick = () => {
            registerModal.style.display = 'block';
            disableBackgroundScroll();
        };
    }

    if (loginBtn && loginModal) {
        loginBtn.onclick = () => {
            loginModal.style.display = 'block';
            disableBackgroundScroll();
        };
    }

    // Close button handlers (old style - if they exist)
    if (closeRegister && registerModal) {
        closeRegister.onclick = () => {
            registerModal.style.display = 'none';
            enableBackgroundScroll();
        };
    }

    if (closeLogin && loginModal) {
        closeLogin.onclick = () => {
            loginModal.style.display = 'none';
            enableBackgroundScroll();
        };
    }

    // New close button handlers
    if (registerCloseBtn && registerModal) {
        registerCloseBtn.onclick = () => {
            registerModal.style.display = 'none';
            enableBackgroundScroll();
        };
    }

    if (loginCloseBtn && loginModal) {
        loginCloseBtn.onclick = () => {
            loginModal.style.display = 'none';
            enableBackgroundScroll();
        };
    }

    // Remove the window click handler to prevent closing by clicking outside
    // window.onclick = e => {
    //     if (e.target === registerModal) registerModal.style.display = 'none';
    //     if (e.target === loginModal) loginModal.style.display = 'none';
    // };
});
