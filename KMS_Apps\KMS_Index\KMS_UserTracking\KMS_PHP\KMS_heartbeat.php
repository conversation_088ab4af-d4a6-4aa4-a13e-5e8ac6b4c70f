<?php
// This script is called by the member page every minute
// to keep the user's session considered "online".

session_start();
$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';

// Check if user is logged in
if (isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true && isset($_SESSION['username'])) {
    
    $username = $_SESSION['username'];
    
    // Update the last_seen timestamp
    $sql = "UPDATE users SET last_seen = NOW() WHERE username = ?";
    
    $link = get_db_connection();
    $stmt = mysqli_prepare($link, $sql);
    
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $username);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        // Respond with success
        http_response_code(200);
        echo json_encode(['status' => 'success']);
    } else {
        // Respond with an error
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => 'Database statement could not be prepared.']);
    }
    
    close_db_connection($link);

} else {
    // Not logged in, respond with an error
    http_response_code(401); // Unauthorized
    echo json_encode(['status' => 'error', 'message' => 'User not authenticated.']);
}
?>