<?php
header('Content-Type: application/json');

$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';

$db_connection = get_db_connection();

try {
    // Consider users online if they were active within the last 5 minutes
    $sql = "
        SELECT COUNT(id) AS online_count
        FROM users
        WHERE last_seen IS NOT NULL
        AND last_seen >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ";

    $result = mysqli_query($db_connection, $sql);

    if (!$result) {
        throw new Exception(mysqli_error($db_connection));
    }

    $row = mysqli_fetch_assoc($result);
    $online_users = $row ? (int)$row['online_count'] : 0;

    echo json_encode(['online_users' => $online_users]);

} catch (Exception $e) {
    error_log("Online Users Script Error: " . $e->getMessage());
    echo json_encode(['error' => 'Database query failed.', 'online_users' => 'N/A']);
} finally {
    if (isset($db_connection)) {
        close_db_connection($db_connection);
    }
}