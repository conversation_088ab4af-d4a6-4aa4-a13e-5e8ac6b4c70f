document.addEventListener('DOMContentLoaded', function() {
    const pcOrderForm = document.getElementById('pcOrderForm');

    if (pcOrderForm) {
        pcOrderForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const orderDetails = document.getElementById('pcOrderDetails').value.trim();
            if (!orderDetails) {
                // Using the existing custom modal functions from custom-modal.js
                showError('PC Order', 'Please describe the PC you would like to order.');
                return;
            }

            // Using the existing custom modal functions
            showLoading('Submitting...', 'Sending your order request.');

            const formData = new FormData();
            formData.append('order_details', orderDetails);

            fetch('KMS_Apps/KMS_Member/KMS_Orders/KMS_PHP/KMS_submit_pc_order.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideModal();
                if (data.success) {
                    showSuccess('Request Sent!', 'We have received your request. We will provide a quote in your member area shortly.');
                    pcOrderForm.reset();
                } else {
                    showError('Submission Failed', data.message || 'An unknown error occurred.');
                }
            })
            .catch(error => {
                hideModal();
                console.error('Error:', error);
                showError('Network Error', 'Could not submit your request. Please check your connection and try again.');
            });
        });
    }
});