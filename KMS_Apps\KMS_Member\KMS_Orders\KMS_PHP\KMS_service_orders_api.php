<?php
/**
 * Service Orders API
 * Handles service order creation, management, and payment processing
 */

session_start();
$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Member' . DIRECTORY_SEPARATOR . 'KMS_Wallet' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_credit_system.php';

header('Content-Type: application/json');

// Check if user is logged in
function checkUserAccess() {
    if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
        echo json_encode(['success' => false, 'message' => 'Please login first']);
        exit;
    }
}

// Check admin access
function checkAdminAccess() {
    if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || !$_SESSION["is_admin"]) {
        echo json_encode(['success' => false, 'message' => 'Admin access required']);
        exit;
    }
}

// Handle different API actions
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'create_order':
        checkUserAccess();
        createOrder();
        break;
    case 'get_user_orders':
        checkUserAccess();
        getUserOrders();
        break;
    case 'get_order':
        checkUserAccess();
        getOrder();
        break;
    case 'cancel_order':
        checkUserAccess();
        cancelOrder();
        break;
    case 'admin_get_orders':
        checkAdminAccess();
        adminGetOrders();
        break;
    case 'admin_update_order':
        checkAdminAccess();
        adminUpdateOrder();
        break;
    case 'calculate_price':
        calculatePrice();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
}

/**
 * Create a new service order
 */
function createOrder() {
    global $link;
    
    $user_id = $_SESSION["id"];
    $service_category = $_POST['service_category'] ?? '';
    $order_items = $_POST['order_items'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $use_credit = isset($_POST['use_credit']) ? (bool)$_POST['use_credit'] : false;
    
    if (!$service_category || !$order_items) {
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
        return;
    }
    
    // Decode order items
    $items = json_decode($order_items, true);
    if (!$items || !is_array($items)) {
        echo json_encode(['success' => false, 'message' => 'Invalid order items format']);
        return;
    }
    
    // Calculate pricing
    $pricing = calculateOrderPricing($items);
    if (!$pricing['success']) {
        echo json_encode($pricing);
        return;
    }
    
    // Generate order number
    $order_number = 'SRV' . date('Ymd') . strtoupper(substr(uniqid(), -6));
    
    mysqli_begin_transaction($link);
    
    try {
        // Create order
        $sql = "INSERT INTO service_orders 
                (order_number, user_id, service_category, order_items, subtotal, 
                 discount_amount, shipping_fee, final_price, notes, status, payment_status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'pending')";
        
        $stmt = execute_query($link, $sql, "sissdddss", [
            $order_number, $user_id, $service_category, $order_items,
            $pricing['subtotal'], $pricing['discount'], $pricing['shipping'],
            $pricing['total'], $notes
        ]);
        
        if (!$stmt) {
            throw new Exception('Failed to create order');
        }
        
        $order_id = mysqli_insert_id($link);
        mysqli_stmt_close($stmt);
        
        // Process payment if using credit
        if ($use_credit && $pricing['total'] > 0) {
            $credit_system = new KMSCreditSystem($link);
            $payment_result = $credit_system->deductCredit(
                $user_id,
                $pricing['total'],
                "Payment for service order #$order_number",
                'service_order',
                $order_id
            );
            
            if ($payment_result['success']) {
                // Update order payment status
                $update_sql = "UPDATE service_orders SET payment_status = 'paid' WHERE id = ?";
                $update_stmt = execute_query($link, $update_sql, "i", [$order_id]);
                if ($update_stmt) {
                    mysqli_stmt_close($update_stmt);
                }
            } else {
                throw new Exception('Payment failed: ' . $payment_result['message']);
            }
        }
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Order created successfully',
            'order_id' => $order_id,
            'order_number' => $order_number,
            'total' => $pricing['total'],
            'payment_processed' => $use_credit
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    
    close_db_connection($link);
}

/**
 * Calculate order pricing
 */
function calculateOrderPricing($items) {
    global $link;
    
    $subtotal = 0;
    $item_details = [];
    
    foreach ($items as $item) {
        $service_id = $item['service_id'] ?? 0;
        $quantity = $item['quantity'] ?? 1;
        
        if (!$service_id || $quantity <= 0) {
            continue;
        }
        
        // Get service price
        $sql = "SELECT * FROM service_prices WHERE id = ? AND is_active = 1";
        $stmt = execute_query($link, $sql, "i", [$service_id]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $service = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            
            if ($service) {
                $item_total = $service['base_price'] * $quantity;
                $subtotal += $item_total;
                
                $item_details[] = [
                    'service_id' => $service_id,
                    'name' => $service['item_name'],
                    'price' => $service['base_price'],
                    'quantity' => $quantity,
                    'total' => $item_total
                ];
            }
        }
    }
    
    // Calculate discounts (10% for orders over $50)
    $discount = 0;
    if ($subtotal > 50) {
        $discount = $subtotal * 0.1;
    }
    
    // Calculate shipping (free for orders over $100)
    $shipping = 0;
    if ($subtotal < 100 && $subtotal > 0) {
        $shipping = 5.00;
    }
    
    $total = $subtotal - $discount + $shipping;
    
    return [
        'success' => true,
        'subtotal' => $subtotal,
        'discount' => $discount,
        'shipping' => $shipping,
        'total' => $total,
        'items' => $item_details
    ];
}

/**
 * Calculate price endpoint (for frontend)
 */
function calculatePrice() {
    $order_items = $_POST['order_items'] ?? '';
    
    if (!$order_items) {
        echo json_encode(['success' => false, 'message' => 'Missing order items']);
        return;
    }
    
    $items = json_decode($order_items, true);
    if (!$items || !is_array($items)) {
        echo json_encode(['success' => false, 'message' => 'Invalid order items format']);
        return;
    }
    
    $pricing = calculateOrderPricing($items);
    echo json_encode($pricing);
    
    close_db_connection($link);
}

/**
 * Get user's orders
 */
function getUserOrders() {
    global $link;
    
    $user_id = $_SESSION["id"];
    
    $sql = "SELECT * FROM service_orders WHERE user_id = ? ORDER BY created_at DESC";
    $stmt = execute_query($link, $sql, "i", [$user_id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $orders = mysqli_fetch_all($result, MYSQLI_ASSOC);
        mysqli_stmt_close($stmt);
        
        // Decode JSON fields
        foreach ($orders as &$order) {
            $order['order_items'] = json_decode($order['order_items'], true);
        }
        
        echo json_encode(['success' => true, 'orders' => $orders]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch orders']);
    }
    
    close_db_connection($link);
}

/**
 * Get single order
 */
function getOrder() {
    global $link;
    
    $order_id = $_GET['order_id'] ?? 0;
    $user_id = $_SESSION["id"];
    
    if (!$order_id) {
        echo json_encode(['success' => false, 'message' => 'Order ID required']);
        return;
    }
    
    $sql = "SELECT * FROM service_orders WHERE id = ? AND user_id = ?";
    $stmt = execute_query($link, $sql, "ii", [$order_id, $user_id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $order = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if ($order) {
            $order['order_items'] = json_decode($order['order_items'], true);
            echo json_encode(['success' => true, 'order' => $order]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Order not found']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch order']);
    }
    
    close_db_connection($link);
}

/**
 * Cancel order
 */
function cancelOrder() {
    global $link;
    
    $order_id = $_POST['order_id'] ?? 0;
    $user_id = $_SESSION["id"];
    
    if (!$order_id) {
        echo json_encode(['success' => false, 'message' => 'Order ID required']);
        return;
    }
    
    // Check if order belongs to user and can be cancelled
    $sql = "SELECT * FROM service_orders WHERE id = ? AND user_id = ? AND status IN ('pending', 'confirmed')";
    $stmt = execute_query($link, $sql, "ii", [$order_id, $user_id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $order = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$order) {
            echo json_encode(['success' => false, 'message' => 'Order not found or cannot be cancelled']);
            return;
        }
        
        mysqli_begin_transaction($link);
        
        try {
            // Update order status
            $update_sql = "UPDATE service_orders SET status = 'cancelled' WHERE id = ?";
            $update_stmt = execute_query($link, $update_sql, "i", [$order_id]);
            
            if (!$update_stmt) {
                throw new Exception('Failed to cancel order');
            }
            mysqli_stmt_close($update_stmt);
            
            // Refund if payment was made
            if ($order['payment_status'] === 'paid') {
                $credit_system = new KMSCreditSystem($link);
                $refund_result = $credit_system->addCredit(
                    $user_id,
                    $order['final_price'],
                    'refund',
                    "Refund for cancelled order #" . $order['order_number'],
                    null,
                    null,
                    'refund'
                );
                
                if (!$refund_result['success']) {
                    throw new Exception('Failed to process refund');
                }
                
                // Update payment status
                $payment_sql = "UPDATE service_orders SET payment_status = 'refunded' WHERE id = ?";
                $payment_stmt = execute_query($link, $payment_sql, "i", [$order_id]);
                if ($payment_stmt) {
                    mysqli_stmt_close($payment_stmt);
                }
            }
            
            mysqli_commit($link);
            echo json_encode(['success' => true, 'message' => 'Order cancelled successfully']);
            
        } catch (Exception $e) {
            mysqli_rollback($link);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch order']);
    }
    
    close_db_connection($link);
}

/**
 * Admin get all orders
 */
function adminGetOrders() {
    global $link;
    
    $sql = "SELECT so.*, u.username, u.email 
            FROM service_orders so 
            LEFT JOIN users u ON so.user_id = u.id 
            ORDER BY so.created_at DESC";
    
    $result = mysqli_query($link, $sql);
    
    if ($result) {
        $orders = mysqli_fetch_all($result, MYSQLI_ASSOC);
        
        // Decode JSON fields
        foreach ($orders as &$order) {
            $order['order_items'] = json_decode($order['order_items'], true);
        }
        
        echo json_encode(['success' => true, 'orders' => $orders]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch orders']);
    }
    
    close_db_connection($link);
}

/**
 * Admin update order
 */
function adminUpdateOrder() {
    global $link;
    
    $order_id = $_POST['order_id'] ?? 0;
    $status = $_POST['status'] ?? '';
    $admin_adjusted_price = $_POST['admin_adjusted_price'] ?? null;
    $admin_adjustment_reason = $_POST['admin_adjustment_reason'] ?? '';
    
    if (!$order_id) {
        echo json_encode(['success' => false, 'message' => 'Order ID required']);
        return;
    }
    
    $sql = "UPDATE service_orders SET ";
    $params = [];
    $types = "";
    
    if ($status) {
        $sql .= "status = ?, ";
        $params[] = $status;
        $types .= "s";
    }
    
    if ($admin_adjusted_price !== null) {
        $sql .= "admin_adjusted_price = ?, admin_adjustment_reason = ?, ";
        $params[] = floatval($admin_adjusted_price);
        $params[] = $admin_adjustment_reason;
        $types .= "ds";
    }
    
    $sql = rtrim($sql, ', ') . " WHERE id = ?";
    $params[] = $order_id;
    $types .= "i";
    
    $stmt = execute_query($link, $sql, $types, $params);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Order updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update order']);
    }
    
    close_db_connection($link);
}
?>
