<?php
/**
 * KMS Deposit API
 * Handles deposit requests and payment processing
 */

session_start();
$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Member' . DIRECTORY_SEPARATOR . 'KMS_Wallet' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_credit_system.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Member' . DIRECTORY_SEPARATOR . 'KMS_Payment' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_payment_processor.php';

// Check if user is logged in
if (!isset($_SESSION['id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['id'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

header('Content-Type: application/json');

switch ($action) {
    case 'get_payment_methods':
        getPaymentMethods($credit_system);
        break;
        
    case 'create_deposit':
        createDeposit($credit_system, $user_id);
        break;
        
    case 'get_deposit_history':
        getDepositHistory($user_id);
        break;
        
    case 'cancel_deposit':
        cancelDeposit($user_id);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Get available payment methods
 */
function getPaymentMethods($credit_system) {
    $methods = [
        [
            'id' => 'paypal',
            'name' => 'PayPal',
            'display_name' => 'PayPal',
            'min_amount' => 10.00,
            'max_amount' => 2000.00,
            'fee_percentage' => 2.9,
            'fee_fixed' => 0.30,
            'is_active' => true,
            'description' => 'Secure payment via PayPal'
        ],
        [
            'id' => 'stripe',
            'name' => 'Credit Card',
            'display_name' => 'Credit/Debit Card',
            'min_amount' => 10.00,
            'max_amount' => 2000.00,
            'fee_percentage' => 2.9,
            'fee_fixed' => 0.30,
            'is_active' => true,
            'description' => 'Visa, MasterCard, American Express'
        ],
        [
            'id' => 'bank_transfer',
            'name' => 'Bank Transfer',
            'display_name' => 'Bank Transfer',
            'min_amount' => 50.00,
            'max_amount' => 5000.00,
            'fee_percentage' => 0.0,
            'fee_fixed' => 0.0,
            'is_active' => true,
            'description' => 'Direct bank transfer (manual verification required)'
        ],
        [
            'id' => 'crypto',
            'name' => 'Cryptocurrency',
            'display_name' => 'Bitcoin/Ethereum',
            'min_amount' => 25.00,
            'max_amount' => 10000.00,
            'fee_percentage' => 1.0,
            'fee_fixed' => 0.0,
            'is_active' => true,
            'description' => 'Bitcoin, Ethereum supported'
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'payment_methods' => $methods
    ]);
}

/**
 * Create a new deposit request
 */
function createDeposit($credit_system, $user_id) {
    $amount = floatval($_POST['amount'] ?? 0);
    $payment_method = trim($_POST['payment_method'] ?? '');
    $total_amount = floatval($_POST['total_amount'] ?? 0);

    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid deposit amount']);
        return;
    }

    if (empty($payment_method)) {
        echo json_encode(['success' => false, 'message' => 'Payment method is required']);
        return;
    }

    // Validate amount limits
    $limits = getPaymentLimits($payment_method);
    if ($amount < $limits['min_amount']) {
        echo json_encode([
            'success' => false,
            'message' => "Minimum deposit amount is $" . number_format($limits['min_amount'], 2)
        ]);
        return;
    }

    if ($amount > $limits['max_amount']) {
        echo json_encode([
            'success' => false,
            'message' => "Maximum deposit amount is $" . number_format($limits['max_amount'], 2)
        ]);
        return;
    }

    global $link;

    try {
        // Initialize payment processor
        $payment_processor = new PaymentProcessor($link);

        // Create payment session
        $payment_result = $payment_processor->createDepositPayment($user_id, $amount, $payment_method, $total_amount);

        if ($payment_result['success']) {
            // Generate unique deposit request ID
            $deposit_id = $payment_result['deposit_id'] ?? ('DEP' . date('Ymd') . strtoupper(substr(uniqid(), -6)));

            // Calculate fees
            $fee_info = calculateDepositFees($amount, $payment_method);

            // Store deposit request
            $sql = "INSERT INTO deposit_requests (user_id, deposit_id, amount, payment_method,
                                                processing_fee, total_amount, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())";

            $stmt = execute_query($link, $sql, "isdsdd", [
                $user_id, $deposit_id, $amount, $payment_method,
                $fee_info['processing_fee'], $total_amount
            ]);

            if ($stmt) {
                mysqli_stmt_close($stmt);

                $response = [
                    'success' => true,
                    'message' => 'Deposit request created successfully',
                    'deposit_id' => $deposit_id,
                    'amount' => number_format($amount, 2),
                    'total_amount' => number_format($total_amount, 2),
                    'fee_info' => $fee_info
                ];

                // Add payment URL for automated processors
                if (isset($payment_result['payment_url'])) {
                    $response['payment_url'] = $payment_result['payment_url'];
                }

                // Add manual payment instructions if needed
                if (isset($payment_result['manual_payment']) && $payment_result['manual_payment']) {
                    $response['manual_payment'] = true;
                    $response['instructions'] = $payment_result['instructions'];
                }

                echo json_encode($response);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to store deposit request']);
            }
        } else {
            echo json_encode($payment_result);
        }

    } catch (Exception $e) {
        error_log("Deposit creation error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Payment processor temporarily unavailable']);
    }
}

/**
 * Get deposit history
 */
function getDepositHistory($user_id) {
    global $link;
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    $sql = "SELECT * FROM deposit_requests 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = execute_query($link, $sql, "iii", [$user_id, $limit, $offset]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $deposits = [];
    
    while ($row = mysqli_fetch_assoc($result)) {
        $deposits[] = [
            'id' => $row['id'],
            'deposit_id' => $row['deposit_id'],
            'amount' => number_format($row['amount'], 2),
            'net_amount' => number_format($row['net_amount'], 2),
            'payment_method' => $row['payment_method'],
            'status' => $row['status'],
            'status_display' => getDepositStatusDisplay($row['status']),
            'created_at' => date('Y-m-d H:i:s', strtotime($row['created_at'])),
            'processed_at' => $row['processed_at'] ? date('Y-m-d H:i:s', strtotime($row['processed_at'])) : null,
            'completed_at' => $row['completed_at'] ? date('Y-m-d H:i:s', strtotime($row['completed_at'])) : null,
            'admin_notes' => $row['admin_notes'],
            'total_fee' => number_format($row['total_fee'], 2)
        ];
    }
    
    mysqli_stmt_close($stmt);
    
    echo json_encode([
        'success' => true,
        'deposits' => $deposits,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'has_more' => count($deposits) == $limit
        ]
    ]);
}

/**
 * Cancel pending deposit
 */
function cancelDeposit($user_id) {
    global $link;
    
    $deposit_id = trim($_POST['deposit_id'] ?? '');
    
    if (empty($deposit_id)) {
        echo json_encode(['success' => false, 'message' => 'Deposit ID is required']);
        return;
    }
    
    $sql = "UPDATE deposit_requests 
            SET status = 'cancelled', updated_at = NOW() 
            WHERE deposit_id = ? AND user_id = ? AND status = 'pending'";
    
    $stmt = execute_query($link, $sql, "si", [$deposit_id, $user_id]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $affected_rows = mysqli_stmt_affected_rows($stmt);
    mysqli_stmt_close($stmt);
    
    if ($affected_rows > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'Deposit request cancelled successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Deposit request not found or cannot be cancelled'
        ]);
    }
}

/**
 * Get payment limits for a specific method
 */
function getPaymentLimits($payment_method) {
    $limits = [
        'paypal' => ['min_amount' => 10.00, 'max_amount' => 2000.00],
        'stripe' => ['min_amount' => 10.00, 'max_amount' => 2000.00],
        'bank_transfer' => ['min_amount' => 50.00, 'max_amount' => 5000.00],
        'crypto' => ['min_amount' => 25.00, 'max_amount' => 10000.00]
    ];
    
    return $limits[$payment_method] ?? ['min_amount' => 10.00, 'max_amount' => 1000.00];
}

/**
 * Calculate deposit fees
 */
function calculateDepositFees($amount, $payment_method) {
    $fee_rates = [
        'paypal' => ['percentage' => 2.9, 'fixed' => 0.30],
        'stripe' => ['percentage' => 2.9, 'fixed' => 0.30],
        'bank_transfer' => ['percentage' => 0.0, 'fixed' => 0.0],
        'crypto' => ['percentage' => 1.0, 'fixed' => 0.0]
    ];
    
    $rates = $fee_rates[$payment_method] ?? ['percentage' => 2.9, 'fixed' => 0.30];
    
    $processing_fee = ($amount * $rates['percentage'] / 100) + $rates['fixed'];
    $platform_fee = 0.0; // Can add platform fees here
    $total_fee = $processing_fee + $platform_fee;
    
    return [
        'processing_fee' => $processing_fee,
        'platform_fee' => $platform_fee,
        'total_fee' => $total_fee,
        'processing_fee_display' => number_format($processing_fee, 2),
        'platform_fee_display' => number_format($platform_fee, 2),
        'total_fee_display' => number_format($total_fee, 2)
    ];
}

/**
 * Generate payment instructions
 */
function generatePaymentInstructions($payment_method, $amount, $deposit_id) {
    switch ($payment_method) {
        case 'paypal':
            return [
                'method' => 'PayPal',
                'instructions' => [
                    'Send payment to: <EMAIL>',
                    'Amount: $' . number_format($amount, 2),
                    'Reference: ' . $deposit_id,
                    'Please include your deposit ID in the payment note'
                ],
                'estimated_time' => '5-15 minutes'
            ];
            
        case 'stripe':
            return [
                'method' => 'Credit/Debit Card',
                'instructions' => [
                    'You will be redirected to our secure payment processor',
                    'Enter your card details to complete payment',
                    'Reference: ' . $deposit_id
                ],
                'estimated_time' => '1-5 minutes'
            ];
            
        case 'bank_transfer':
            return [
                'method' => 'Bank Transfer',
                'instructions' => [
                    'Bank: KMS Financial Services',
                    'Account Number: **********',
                    'Routing Number: *********',
                    'Amount: $' . number_format($amount, 2),
                    'Reference: ' . $deposit_id,
                    'Please include deposit ID in transfer memo'
                ],
                'estimated_time' => '1-3 business days'
            ];
            
        case 'crypto':
            return [
                'method' => 'Cryptocurrency',
                'instructions' => [
                    'Bitcoin Address: **********************************',
                    'Ethereum Address: ******************************************',
                    'Amount: $' . number_format($amount, 2),
                    'Reference: ' . $deposit_id,
                    'Please send exact USD equivalent in crypto'
                ],
                'estimated_time' => '10-60 minutes'
            ];
            
        default:
            return [
                'method' => 'Unknown',
                'instructions' => ['Please contact support for payment instructions'],
                'estimated_time' => 'N/A'
            ];
    }
}

/**
 * Get display text for deposit status
 */
function getDepositStatusDisplay($status) {
    $statuses = [
        'pending' => 'Pending Payment',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled',
        'expired' => 'Expired'
    ];
    
    return $statuses[$status] ?? ucfirst($status);
}
?>