<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Address Fields to Users Table</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Add Address Fields to Users Table</h1>
    
    <?php
    /**
     * Add address fields to users table
     * This script adds street_address, city, state, and zip_code fields to the users table
     */

    require_once '../KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';
    require_once '../KMS_Apps/KMS_Core/KMS_Functions/KMS_PHP/KMS_functions.php';

    echo "<div class='info'><strong>Adding address fields to users table...</strong></div><br>";

    try {
        // Check if fields already exist
        $check_sql = "SHOW COLUMNS FROM users LIKE 'street_address'";
        $result = mysqli_query($link, $check_sql);
        
        if (mysqli_num_rows($result) > 0) {
            echo "<div class='info'>Address fields already exist in users table.</div><br>";
        } else {
            // Add address fields
            $alter_sql = "
                ALTER TABLE users
                ADD COLUMN street_address VARCHAR(255) NULL AFTER phone_number,
                ADD COLUMN city VARCHAR(100) NULL AFTER street_address,
                ADD COLUMN state VARCHAR(50) NULL AFTER city,
                ADD COLUMN zip_code VARCHAR(10) NULL AFTER state
            ";
            
            if (mysqli_query($link, $alter_sql)) {
                echo "<div class='success'><strong>Successfully added address fields to users table:</strong></div>";
                echo "<ul>";
                echo "<li>street_address (VARCHAR 255)</li>";
                echo "<li>city (VARCHAR 100)</li>";
                echo "<li>state (VARCHAR 50)</li>";
                echo "<li>zip_code (VARCHAR 10)</li>";
                echo "</ul>";
            } else {
                echo "<div class='error'>Error adding address fields: " . mysqli_error($link) . "</div>";
                exit(1);
            }
        }
        
        // Verify the changes
        $verify_sql = "DESCRIBE users";
        $result = mysqli_query($link, $verify_sql);
        
        echo "<h2>Current users table structure:</h2>";
        echo "<pre>";
        echo sprintf("%-20s %-20s %-8s %-8s %-10s %s\n",
            "Field", "Type", "Null", "Key", "Default", "Extra");
        echo str_repeat("-", 80) . "\n";
        
        while ($row = mysqli_fetch_assoc($result)) {
            printf("%-20s %-20s %-8s %-8s %-10s %s\n",
                $row['Field'],
                $row['Type'],
                $row['Null'],
                $row['Key'],
                $row['Default'] ?? 'NULL',
                $row['Extra']
            );
        }
        echo "</pre>";
        
        echo "<div class='success'><strong>Address fields have been successfully added!</strong></div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>Error: " . $e->getMessage() . "</div>";
        exit(1);
    } finally {
        close_db_connection($link);
    }
    ?>
</body>
</html>