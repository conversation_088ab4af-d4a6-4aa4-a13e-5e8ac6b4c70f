<?php
// Insert current PC components from member.php into database
require_once '../KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

echo "<h2>Inserting Current PC Components...</h2>";

try {
    // First, let's get the category IDs
    $categories = [];
    $cat_query = "SELECT id, category_name FROM pc_component_categories";
    $cat_result = mysqli_query($link, $cat_query);
    
    if ($cat_result) {
        while ($row = mysqli_fetch_assoc($cat_result)) {
            $categories[strtolower($row['category_name'])] = $row['id'];
        }
    }
    
    echo "<h3>Available Categories:</h3>";
    foreach ($categories as $name => $id) {
        echo "<p>$name: ID $id</p>";
    }
    
    // Define the components from member.php
    $components_data = [
        // CPUs
        [
            'category' => 'cpu',
            'name' => 'Intel 265K',
            'brand' => 'Intel',
            'model' => '265K',
            'description' => 'High Performance',
            'base_price' => 380.00,
            'current_price' => 380.00,
            'specifications' => json_encode([
                'cores' => 14,
                'threads' => 20,
                'base_clock' => '3.5GHz',
                'boost_clock' => '5.1GHz',
                'socket' => 'LGA1700'
            ])
        ],
        [
            'category' => 'cpu',
            'name' => 'Intel 285K',
            'brand' => 'Intel',
            'model' => '285K',
            'description' => 'Ultra Performance for both Gaming and Content Creation',
            'base_price' => 760.00,
            'current_price' => 760.00,
            'specifications' => json_encode([
                'cores' => 24,
                'threads' => 32,
                'base_clock' => '3.7GHz',
                'boost_clock' => '5.7GHz',
                'socket' => 'LGA1700'
            ])
        ],
        [
            'category' => 'cpu',
            'name' => 'AMD 7800X3D',
            'brand' => 'AMD',
            'model' => '7800X3D',
            'description' => 'Great Value Gaming CPU',
            'base_price' => 481.00,
            'current_price' => 481.00,
            'specifications' => json_encode([
                'cores' => 8,
                'threads' => 16,
                'base_clock' => '4.2GHz',
                'boost_clock' => '5.0GHz',
                'socket' => 'AM5',
                '3d_cache' => '96MB'
            ])
        ],
        [
            'category' => 'cpu',
            'name' => 'AMD 9800X3D',
            'brand' => 'AMD',
            'model' => '9800X3D',
            'description' => 'TOP1 Gaming CPU',
            'base_price' => 608.00,
            'current_price' => 608.00,
            'specifications' => json_encode([
                'cores' => 8,
                'threads' => 16,
                'base_clock' => '4.7GHz',
                'boost_clock' => '5.2GHz',
                'socket' => 'AM5',
                '3d_cache' => '96MB'
            ])
        ],
        [
            'category' => 'cpu',
            'name' => 'AMD 9950X3D',
            'brand' => 'AMD',
            'model' => '9950X3D',
            'description' => 'Ultimate Performance for both Gaming and Content Creation',
            'base_price' => 886.00,
            'current_price' => 886.00,
            'specifications' => json_encode([
                'cores' => 16,
                'threads' => 32,
                'base_clock' => '4.3GHz',
                'boost_clock' => '5.7GHz',
                'socket' => 'AM5',
                '3d_cache' => '128MB'
            ])
        ],
        
        // GPUs
        [
            'category' => 'gpu',
            'name' => 'RTX 5070',
            'brand' => 'NVIDIA',
            'model' => 'RTX 5070',
            'description' => 'Good for 1440p or 4K Gaming',
            'base_price' => 850.00,
            'current_price' => 850.00,
            'specifications' => json_encode([
                'memory' => '12GB GDDR6X',
                'memory_bus' => '192-bit',
                'boost_clock' => '2610MHz',
                'cuda_cores' => 6144,
                'rt_cores' => '3rd Gen',
                'tensor_cores' => '4th Gen'
            ])
        ],
        [
            'category' => 'gpu',
            'name' => 'RTX 5070 Ti',
            'brand' => 'NVIDIA',
            'model' => 'RTX 5070 Ti',
            'description' => 'Good for 4K Gaming',
            'base_price' => 1299.00,
            'current_price' => 1299.00,
            'specifications' => json_encode([
                'memory' => '16GB GDDR6X',
                'memory_bus' => '256-bit',
                'boost_clock' => '2750MHz',
                'cuda_cores' => 8960,
                'rt_cores' => '3rd Gen',
                'tensor_cores' => '4th Gen'
            ])
        ],
        [
            'category' => 'gpu',
            'name' => 'RTX 5080',
            'brand' => 'NVIDIA',
            'model' => 'RTX 5080',
            'description' => 'Good for 4K Gaming at higher FPS',
            'base_price' => 2299.00,
            'current_price' => 2299.00,
            'specifications' => json_encode([
                'memory' => '16GB GDDR6X',
                'memory_bus' => '256-bit',
                'boost_clock' => '2900MHz',
                'cuda_cores' => 10752,
                'rt_cores' => '3rd Gen',
                'tensor_cores' => '4th Gen'
            ])
        ],
        [
            'category' => 'gpu',
            'name' => 'RTX 5090',
            'brand' => 'NVIDIA',
            'model' => 'RTX 5090',
            'description' => 'Ultimate 4K Gaming at all settings maxed out',
            'base_price' => 3999.00,
            'current_price' => 3999.00,
            'specifications' => json_encode([
                'memory' => '32GB GDDR6X',
                'memory_bus' => '512-bit',
                'boost_clock' => '2950MHz',
                'cuda_cores' => 21760,
                'rt_cores' => '3rd Gen',
                'tensor_cores' => '4th Gen'
            ])
        ],

        // RAM
        [
            'category' => 'ram',
            'name' => '32GB DDR5',
            'brand' => 'Corsair',
            'model' => 'Vengeance DDR5-5600',
            'description' => 'Standard',
            'base_price' => 199.00,
            'current_price' => 199.00,
            'specifications' => json_encode([
                'capacity' => '32GB',
                'type' => 'DDR5',
                'speed' => '5600MHz',
                'modules' => '2x16GB',
                'cas_latency' => 'CL36'
            ])
        ],
        [
            'category' => 'ram',
            'name' => '48GB DDR5',
            'brand' => 'Corsair',
            'model' => 'Vengeance DDR5-5600',
            'description' => 'Enhanced',
            'base_price' => 299.00,
            'current_price' => 299.00,
            'specifications' => json_encode([
                'capacity' => '48GB',
                'type' => 'DDR5',
                'speed' => '5600MHz',
                'modules' => '2x24GB',
                'cas_latency' => 'CL36'
            ])
        ],
        [
            'category' => 'ram',
            'name' => '64GB DDR5',
            'brand' => 'Corsair',
            'model' => 'Vengeance DDR5-5600',
            'description' => 'Professional',
            'base_price' => 399.00,
            'current_price' => 399.00,
            'specifications' => json_encode([
                'capacity' => '64GB',
                'type' => 'DDR5',
                'speed' => '5600MHz',
                'modules' => '2x32GB',
                'cas_latency' => 'CL36'
            ])
        ],
        [
            'category' => 'ram',
            'name' => '96GB DDR5',
            'brand' => 'Corsair',
            'model' => 'Vengeance DDR5-5600',
            'description' => 'Workstation',
            'base_price' => 599.00,
            'current_price' => 599.00,
            'specifications' => json_encode([
                'capacity' => '96GB',
                'type' => 'DDR5',
                'speed' => '5600MHz',
                'modules' => '2x48GB',
                'cas_latency' => 'CL36'
            ])
        ],
        [
            'category' => 'ram',
            'name' => '128GB DDR5',
            'brand' => 'Corsair',
            'model' => 'Vengeance DDR5-5600',
            'description' => 'Extreme',
            'base_price' => 799.00,
            'current_price' => 799.00,
            'specifications' => json_encode([
                'capacity' => '128GB',
                'type' => 'DDR5',
                'speed' => '5600MHz',
                'modules' => '4x32GB',
                'cas_latency' => 'CL36'
            ])
        ],

        // Storage
        [
            'category' => 'storage',
            'name' => '2TB NVMe SSD',
            'brand' => 'Samsung',
            'model' => '990 PRO',
            'description' => 'Single Drive (7000MB/s)',
            'base_price' => 159.00,
            'current_price' => 159.00,
            'specifications' => json_encode([
                'capacity' => '2TB',
                'type' => 'NVMe SSD',
                'interface' => 'PCIe 4.0',
                'read_speed' => '7000MB/s',
                'write_speed' => '6900MB/s',
                'form_factor' => 'M.2 2280'
            ])
        ],
        [
            'category' => 'storage',
            'name' => '4TB NVMe SSD',
            'brand' => 'Samsung',
            'model' => '990 PRO',
            'description' => 'Single Drive (7000MB/s)',
            'base_price' => 339.00,
            'current_price' => 339.00,
            'specifications' => json_encode([
                'capacity' => '4TB',
                'type' => 'NVMe SSD',
                'interface' => 'PCIe 4.0',
                'read_speed' => '7000MB/s',
                'write_speed' => '6900MB/s',
                'form_factor' => 'M.2 2280'
            ])
        ],
        [
            'category' => 'storage',
            'name' => '2TB + 4TB NVMe',
            'brand' => 'Samsung',
            'model' => '990 PRO Dual',
            'description' => 'Dual Drive (7000MB/s each)',
            'base_price' => 499.00,
            'current_price' => 499.00,
            'specifications' => json_encode([
                'capacity' => '6TB Total',
                'type' => 'Dual NVMe SSD',
                'interface' => 'PCIe 4.0',
                'read_speed' => '7000MB/s',
                'write_speed' => '6900MB/s',
                'configuration' => '2TB + 4TB'
            ])
        ],
        [
            'category' => 'storage',
            'name' => '4TB + 4TB NVMe',
            'brand' => 'Samsung',
            'model' => '990 PRO Dual',
            'description' => 'Dual Drive (7000MB/s each)',
            'base_price' => 699.00,
            'current_price' => 699.00,
            'specifications' => json_encode([
                'capacity' => '8TB Total',
                'type' => 'Dual NVMe SSD',
                'interface' => 'PCIe 4.0',
                'read_speed' => '7000MB/s',
                'write_speed' => '6900MB/s',
                'configuration' => '4TB + 4TB'
            ])
        ],

        // PSU
        [
            'category' => 'psu',
            'name' => '850W',
            'brand' => 'Corsair',
            'model' => 'RM850x',
            'description' => '80+ Gold 10 years warranty',
            'base_price' => 169.00,
            'current_price' => 169.00,
            'specifications' => json_encode([
                'wattage' => '850W',
                'efficiency' => '80+ Gold',
                'modular' => 'Fully Modular',
                'warranty' => '10 Years',
                'fan_size' => '135mm'
            ])
        ],
        [
            'category' => 'psu',
            'name' => '1000W',
            'brand' => 'Corsair',
            'model' => 'RM1000x',
            'description' => '80+ Gold 10 years warranty',
            'base_price' => 219.00,
            'current_price' => 219.00,
            'specifications' => json_encode([
                'wattage' => '1000W',
                'efficiency' => '80+ Gold',
                'modular' => 'Fully Modular',
                'warranty' => '10 Years',
                'fan_size' => '135mm'
            ])
        ],
        [
            'category' => 'psu',
            'name' => '1200W',
            'brand' => 'Corsair',
            'model' => 'RM1200x',
            'description' => '80+ Gold 10 years warranty',
            'base_price' => 269.00,
            'current_price' => 269.00,
            'specifications' => json_encode([
                'wattage' => '1200W',
                'efficiency' => '80+ Gold',
                'modular' => 'Fully Modular',
                'warranty' => '10 Years',
                'fan_size' => '135mm'
            ])
        ]
    ];

    $success_count = 0;
    $error_count = 0;
    
    // Insert components
    $insert_sql = "INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, specifications, base_price, current_price, description, description_en, description_zh, stock_quantity, is_active, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($link, $insert_sql);
    
    if (!$stmt) {
        throw new Exception("Failed to prepare statement: " . mysqli_error($link));
    }
    
    foreach ($components_data as $index => $component) {
        $category_id = $categories[strtolower($component['category'])] ?? null;
        
        if (!$category_id) {
            echo "<p style='color: red;'>Category '{$component['category']}' not found for {$component['name']}</p>";
            $error_count++;
            continue;
        }
        
        $stock_quantity = 10;
        $is_active = 1;
        $sort_order = $index + 1;

        mysqli_stmt_bind_param($stmt, "issssssddsssiii",
            $category_id,
            $component['name'],
            $component['name'],
            $component['name'],
            $component['brand'],
            $component['model'],
            $component['specifications'],
            $component['base_price'],
            $component['current_price'],
            $component['description'],
            $component['description'],
            $component['description'],
            $stock_quantity,
            $is_active,
            $sort_order
        );
        
        if (mysqli_stmt_execute($stmt)) {
            echo "<p style='color: green;'>✓ Inserted: {$component['name']} - \${$component['current_price']}</p>";
            $success_count++;
        } else {
            echo "<p style='color: red;'>✗ Failed to insert {$component['name']}: " . mysqli_stmt_error($stmt) . "</p>";
            $error_count++;
        }
    }
    
    mysqli_stmt_close($stmt);
    
    echo "<h3>Component Insertion Complete!</h3>";
    echo "<p>Successfully inserted: $success_count components</p>";
    echo "<p>Failed insertions: $error_count</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

mysqli_close($link);
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
</style>
