<?php
// Insert Pre-built PC configurations from member.php into database
require_once '../KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

echo "<h2>Inserting Pre-built PC Configurations...</h2>";

try {
    // Define the pre-built configurations from member.php
    $prebuilt_configs = [
        [
            'name' => 'Gaming Essential',
            'description' => 'Perfect entry-level gaming build',
            'tier' => 'entry',
            'primary_use' => 'gaming',
            'specs' => [
                'Case' => 'Medium White',
                'CPU' => 'Intel 265K',
                'GPU' => 'RTX 5070',
                'RAM' => '32GB DDR5',
                'Storage' => '2TB NVMe SSD',
                'PSU' => '850W 80+ Gold'
            ],
            'base_price' => 2599.00,
            'current_price' => 2299.00,
            'discount' => 12
        ],
        [
            'name' => 'Gaming Performance',
            'description' => 'High-performance gaming PC',
            'tier' => 'mid',
            'primary_use' => 'gaming',
            'specs' => [
                'Case' => 'Medium White',
                'CPU' => 'AMD 7800X3D',
                'GPU' => 'RTX 5070 Ti',
                'RAM' => '48GB DDR5',
                'Storage' => '2TB + 4TB NVMe',
                'PSU' => '1000W 80+ Gold'
            ],
            'base_price' => 3699.00,
            'current_price' => 3299.00,
            'discount' => 11
        ],
        [
            'name' => 'Gaming Ultimate',
            'description' => 'No compromises gaming beast',
            'tier' => 'high',
            'primary_use' => 'gaming',
            'specs' => [
                'Case' => 'Medium White',
                'CPU' => 'AMD 9800X3D',
                'GPU' => 'RTX 5080',
                'RAM' => '64GB DDR5',
                'Storage' => '4TB NVMe',
                'PSU' => '1200W 80+ Gold'
            ],
            'base_price' => 5599.00,
            'current_price' => 4999.00,
            'discount' => 11
        ],
        [
            'name' => 'Creator Workstation',
            'description' => 'Professional content creation',
            'tier' => 'extreme',
            'primary_use' => 'both',
            'specs' => [
                'Case' => 'Medium White',
                'CPU' => 'AMD 9950X3D',
                'GPU' => 'RTX 5090',
                'RAM' => '128GB DDR5',
                'Storage' => '4TB + 4TB NVMe',
                'PSU' => '1200W 80+ Gold'
            ],
            'base_price' => 8999.00,
            'current_price' => 7999.00,
            'discount' => 11
        ]
    ];
    
    $success_count = 0;
    $error_count = 0;
    
    // Insert pre-built configurations
    $insert_sql = "INSERT INTO pc_prebuilt_configs (config_name, config_name_en, config_name_zh, description, description_en, description_zh, tier, primary_use, components, specifications_summary, base_price, current_price, discount_percentage, is_active, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($link, $insert_sql);
    
    if (!$stmt) {
        throw new Exception("Failed to prepare statement: " . mysqli_error($link));
    }
    
    foreach ($prebuilt_configs as $index => $config) {
        // Create components JSON (simplified for now)
        $components_json = json_encode([
            'cpu' => $config['specs']['CPU'],
            'gpu' => $config['specs']['GPU'],
            'ram' => $config['specs']['RAM'],
            'storage' => $config['specs']['Storage'],
            'psu' => $config['specs']['PSU'],
            'case' => $config['specs']['Case']
        ]);
        
        $specs_summary_json = json_encode($config['specs']);
        $is_active = 1;
        $sort_order = $index + 1;
        
        mysqli_stmt_bind_param($stmt, "ssssssssssdddii", 
            $config['name'],
            $config['name'],
            $config['name'],
            $config['description'],
            $config['description'],
            $config['description'],
            $config['tier'],
            $config['primary_use'],
            $components_json,
            $specs_summary_json,
            $config['base_price'],
            $config['current_price'],
            $config['discount'],
            $is_active,
            $sort_order
        );
        
        if (mysqli_stmt_execute($stmt)) {
            echo "<p style='color: green;'>✓ Inserted: {$config['name']} - \${$config['current_price']} (was \${$config['base_price']}, {$config['discount']}% off)</p>";
            $success_count++;
        } else {
            echo "<p style='color: red;'>✗ Failed to insert {$config['name']}: " . mysqli_stmt_error($stmt) . "</p>";
            $error_count++;
        }
    }
    
    mysqli_stmt_close($stmt);
    
    echo "<h3>Pre-built Configuration Insertion Complete!</h3>";
    echo "<p>Successfully inserted: $success_count configurations</p>";
    echo "<p>Failed insertions: $error_count</p>";
    
    // Display inserted configurations
    $check_configs = "SELECT * FROM pc_prebuilt_configs ORDER BY sort_order";
    $result = mysqli_query($link, $check_configs);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<h4>Inserted Pre-built Configurations:</h4>";
        while ($config = mysqli_fetch_assoc($result)) {
            echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 5px solid #007bff;'>";
            echo "<h5 style='margin: 0 0 10px 0; color: #007bff;'>" . htmlspecialchars($config['config_name']) . "</h5>";
            echo "<p><strong>Tier:</strong> " . ucfirst($config['tier']) . " | <strong>Use:</strong> " . ucfirst(str_replace('_', ' ', $config['primary_use'])) . "</p>";
            echo "<p><strong>Price:</strong> $" . number_format($config['current_price'], 2);
            if ($config['discount_percentage'] > 0) {
                echo " <span style='color: #dc3545;'>(" . $config['discount_percentage'] . "% OFF from $" . number_format($config['base_price'], 2) . ")</span>";
            }
            echo "</p>";
            echo "<p><strong>Description:</strong> " . htmlspecialchars($config['description']) . "</p>";
            
            $specs = json_decode($config['specifications_summary'], true);
            if ($specs) {
                echo "<p><strong>Specifications:</strong></p>";
                echo "<ul>";
                foreach ($specs as $component => $spec) {
                    echo "<li><strong>$component:</strong> $spec</li>";
                }
                echo "</ul>";
            }
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

mysqli_close($link);
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4, h5 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
li { margin: 3px 0; }
</style>
