<?php
require_once '../KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Only POST method allowed']);
    exit;
}

$action = $_POST['action'] ?? '';
$local_link = get_db_connection();

if (!$local_link) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

switch ($action) {
    case 'get_base_pricing':
        get_base_pricing($local_link);
        break;
    case 'update_base_pricing':
        update_base_pricing($local_link);
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

close_db_connection($local_link);

function get_base_pricing($local_link) {
    $sql = "SELECT * FROM pc_base_pricing ORDER BY pricing_type, config_key";
    $result = mysqli_query($local_link, $sql);
    
    if (!$result) {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch pricing data']);
        return;
    }
    
    $pricing_data = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $pricing_data[] = $row;
    }
    
    echo json_encode(['success' => true, 'pricing_data' => $pricing_data]);
}

function update_base_pricing($local_link) {
    $id = intval($_POST['id'] ?? 0);
    $price = floatval($_POST['price'] ?? 0);
    $description = $_POST['description'] ?? '';
    $description_en = $_POST['description_en'] ?? $description;
    $description_zh = $_POST['description_zh'] ?? $description;
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    if (!$id || $price < 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid input data']);
        return;
    }
    
    $sql = "UPDATE pc_base_pricing SET 
            price = ?, description = ?, description_en = ?, description_zh = ?, is_active = ?
            WHERE id = ?";
    
    $stmt = execute_query($local_link, $sql, "dsssii", [
        $price, $description, $description_en, $description_zh, $is_active, $id
    ]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Pricing updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update pricing']);
    }
}

function execute_query($connection, $sql, $types = "", $params = []) {
    $stmt = mysqli_prepare($connection, $sql);
    if (!$stmt) {
        return false;
    }

    if (!empty($params)) {
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }

    if (mysqli_stmt_execute($stmt)) {
        return $stmt;
    } else {
        mysqli_stmt_close($stmt);
        return false;
    }
}
