<?php
require_once '../KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['success' => false, 'message' => 'Only GET method allowed']);
    exit;
}

$local_link = get_db_connection();

if (!$local_link) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Get active base pricing configurations
$sql = "SELECT pricing_type, config_key, price, description, description_en, description_zh 
        FROM pc_base_pricing 
        WHERE is_active = 1 
        ORDER BY pricing_type, config_key";

$result = mysqli_query($local_link, $sql);

if (!$result) {
    echo json_encode(['success' => false, 'message' => 'Failed to fetch pricing data']);
    close_db_connection($local_link);
    exit;
}

$pricing_config = [
    'base_price' => 0,
    'base_description' => '',
    'case_prices' => [
        'small' => 200,
        'medium' => 200,
        'large' => 300
    ],
    'case_descriptions' => [
        'small' => 'Small case - Premium quality',
        'medium' => 'Medium case - Premium quality', 
        'large' => 'Large case - Premium quality'
    ]
];

while ($row = mysqli_fetch_assoc($result)) {
    if ($row['pricing_type'] === 'base_price' && $row['config_key'] === 'detailed_mode') {
        $pricing_config['base_price'] = floatval($row['price']);
        $pricing_config['base_description'] = $row['description'] ?? '';
    } elseif ($row['pricing_type'] === 'case_price') {
        $pricing_config['case_prices'][$row['config_key']] = floatval($row['price']);
        $pricing_config['case_descriptions'][$row['config_key']] = $row['description'] ?? '';
    }
}

close_db_connection($local_link);

echo json_encode([
    'success' => true,
    'pricing_config' => $pricing_config
]);
