<?php
require_once 'config.php';

header('Content-Type: application/json; charset=utf-8');

$local_link = get_db_connection();

if (!$local_link) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Create the table
$sql = "CREATE TABLE IF NOT EXISTS pc_base_pricing (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pricing_type ENUM('base_price', 'case_price') NOT NULL,
    config_key VARCHAR(50) NOT NULL COMMENT 'For base_price: \"detailed_mode\", for case_price: \"small\", \"medium\", \"large\"',
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    description TEXT COMMENT 'What is included in this price',
    description_en TEXT,
    description_zh TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_pricing (pricing_type, config_key),
    INDEX idx_type (pricing_type),
    INDEX idx_active (is_active)
)";

if (!mysqli_query($local_link, $sql)) {
    echo json_encode(['success' => false, 'message' => 'Failed to create table: ' . mysqli_error($local_link)]);
    close_db_connection($local_link);
    exit;
}

// Insert default data
$sql = "INSERT INTO pc_base_pricing (pricing_type, config_key, price, description, description_en, description_zh, is_active) VALUES
('base_price', 'detailed_mode', 0.00, 'Base configuration includes: 10 premium RGB fans, premium RGB power cables, CPU special cooling treatment, premium CPU liquid cooler, GPU Power Adapter, RGB and Fan controller', 'Base configuration includes: 10 premium RGB fans, premium RGB power cables, CPU special cooling treatment, premium CPU liquid cooler, GPU Power Adapter, RGB and Fan controller', '基礎配置包含：十把頂級RGB風扇，頂級RGB電源線，CPU特殊散熱處理，高級CPU水冷散熱器，GPU Power Adapter，RGB and Fan controller', TRUE),
('case_price', 'small', 200.00, 'Small case - Premium quality with excellent aesthetics', 'Small case - Premium quality with excellent aesthetics', '小型機殼 - 頂級品質，外觀精美', TRUE),
('case_price', 'medium', 200.00, 'Medium case - Premium quality with excellent aesthetics', 'Medium case - Premium quality with excellent aesthetics', '中型機殼 - 頂級品質，外觀精美', TRUE),
('case_price', 'large', 300.00, 'Large case - Premium quality with excellent aesthetics', 'Large case - Premium quality with excellent aesthetics', '大型機殼 - 頂級品質，外觀精美', TRUE)
ON DUPLICATE KEY UPDATE 
    price = VALUES(price),
    description = VALUES(description),
    description_en = VALUES(description_en),
    description_zh = VALUES(description_zh),
    is_active = VALUES(is_active)";

if (!mysqli_query($local_link, $sql)) {
    echo json_encode(['success' => false, 'message' => 'Failed to insert default data: ' . mysqli_error($local_link)]);
    close_db_connection($local_link);
    exit;
}

close_db_connection($local_link);

echo json_encode(['success' => true, 'message' => 'Table created and default data inserted successfully']);
?>
