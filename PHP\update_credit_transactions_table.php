<?php
/**
 * Database Update Script for Credit Transactions Table
 * This script adds missing columns to the credit_transactions table
 */

require_once '../KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

echo "<h1>Credit Transactions Table Update</h1>\n";

try {
    // Check if credit_transactions table exists
    $check_table = "SHOW TABLES LIKE 'credit_transactions'";
    $result = mysqli_query($link, $check_table);
    
    if (!$result || mysqli_num_rows($result) == 0) {
        echo "<p style='color: red;'>Error: credit_transactions table does not exist!</p>\n";
        exit;
    }
    
    echo "<p>✅ credit_transactions table found</p>\n";
    
    // Get current table structure
    $describe_result = mysqli_query($link, "DESCRIBE credit_transactions");
    $existing_columns = [];
    
    while ($row = mysqli_fetch_assoc($describe_result)) {
        $existing_columns[] = $row['Field'];
    }
    
    echo "<p>Current columns: " . implode(', ', $existing_columns) . "</p>\n";
    
    // Add missing columns
    $columns_to_add = [
        'payment_method' => "ALTER TABLE credit_transactions ADD COLUMN payment_method VARCHAR(50) DEFAULT NULL COMMENT 'Payment method used'",
        'external_transaction_id' => "ALTER TABLE credit_transactions ADD COLUMN external_transaction_id VARCHAR(100) DEFAULT NULL COMMENT 'External payment system transaction ID'",
        'admin_user_id' => "ALTER TABLE credit_transactions ADD COLUMN admin_user_id INT DEFAULT NULL COMMENT 'Admin user who performed the action'"
    ];
    
    foreach ($columns_to_add as $column => $sql) {
        if (!in_array($column, $existing_columns)) {
            echo "<p>Adding column: $column</p>\n";
            
            if (mysqli_query($link, $sql)) {
                echo "<p style='color: green;'>✅ Successfully added column: $column</p>\n";
            } else {
                echo "<p style='color: red;'>❌ Error adding column $column: " . mysqli_error($link) . "</p>\n";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ Column $column already exists</p>\n";
        }
    }
    
    // Add foreign key constraint for admin_user_id if it doesn't exist
    if (in_array('admin_user_id', $existing_columns)) {
        echo "<p>Checking foreign key constraint for admin_user_id...</p>\n";
        
        $fk_check = "SELECT CONSTRAINT_NAME FROM information_schema.KEY_COLUMN_USAGE 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'credit_transactions' 
                     AND COLUMN_NAME = 'admin_user_id' 
                     AND REFERENCED_TABLE_NAME IS NOT NULL";
        
        $fk_result = mysqli_query($link, $fk_check);
        
        if (mysqli_num_rows($fk_result) == 0) {
            echo "<p>Adding foreign key constraint for admin_user_id...</p>\n";
            
            $fk_sql = "ALTER TABLE credit_transactions ADD CONSTRAINT fk_credit_transactions_admin_user 
                       FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE SET NULL";
            
            if (mysqli_query($link, $fk_sql)) {
                echo "<p style='color: green;'>✅ Successfully added foreign key constraint</p>\n";
            } else {
                echo "<p style='color: red;'>❌ Error adding foreign key constraint: " . mysqli_error($link) . "</p>\n";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ Foreign key constraint already exists</p>\n";
        }
    }
    
    // Update transaction_type enum to include new types
    echo "<p>Updating transaction_type enum...</p>\n";
    
    $update_enum_sql = "ALTER TABLE credit_transactions 
                        MODIFY COLUMN transaction_type ENUM('deposit', 'withdraw', 'spend', 'refund', 'transfer_in', 'transfer_out', 'admin_adjust', 'admin_gift', 'admin_deduct') NOT NULL";
    
    if (mysqli_query($link, $update_enum_sql)) {
        echo "<p style='color: green;'>✅ Successfully updated transaction_type enum</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Error updating transaction_type enum: " . mysqli_error($link) . "</p>\n";
    }
    
    // Show final table structure
    echo "<h2>Final Table Structure:</h2>\n";
    $final_describe = mysqli_query($link, "DESCRIBE credit_transactions");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    
    while ($row = mysqli_fetch_assoc($final_describe)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    echo "<h2>✅ Database update completed successfully!</h2>\n";
    echo "<p>The credit_transactions table has been updated with the following enhancements:</p>\n";
    echo "<ul>\n";
    echo "<li>Added payment_method column for tracking payment methods</li>\n";
    echo "<li>Added external_transaction_id column for external payment system integration</li>\n";
    echo "<li>Added admin_user_id column for tracking admin actions</li>\n";
    echo "<li>Updated transaction_type enum to include admin_gift and admin_deduct</li>\n";
    echo "<li>Added foreign key constraint for admin_user_id</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
} finally {
    close_db_connection($link);
}
?>
