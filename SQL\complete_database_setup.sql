-- =============================================
-- KelvinKMS - Complete Database Setup
-- =============================================

-- Drop existing database if exists
DROP DATABASE IF EXISTS kelvinkms;
CREATE DATABASE kelvinkms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE kelvinkms;

-- Users table for authentication with extended profile
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(50),
    last_name VARCHAR(50),
    nickname VA<PERSON>HAR(50),
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say') DEFAULT 'prefer_not_to_say',
    birthday DATE,
    language VARCHAR(10) DEFAULT 'en',
    email VARCHAR(100) UNIQUE,
    phone_number VA<PERSON>HAR(20),
    street_address VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(10),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    last_seen TIMESTAMP NULL DEFAULT NULL,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_is_active (is_active)
);

-- User Wallets Table - Stores each user's credit balance
CREATE TABLE IF NOT EXISTS user_wallets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    frozen_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Balance locked during pending transactions',
    commission_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Available affiliate commission balance',
    total_deposited DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Total amount ever deposited',
    total_spent DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Total amount ever spent',
    total_commissions DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Total commissions earned',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_balance (balance)
);

-- Credit Transactions Table - Records all credit-related transactions
CREATE TABLE IF NOT EXISTS credit_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL COMMENT 'Unique transaction identifier',
    user_id INT NOT NULL,
    transaction_type ENUM('deposit', 'withdraw', 'spend', 'refund', 'transfer_in', 'transfer_out', 'admin_adjust', 'admin_gift', 'admin_deduct', 'affiliate', 'affiliate_transfer') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    balance_before DECIMAL(10,2) NOT NULL,
    balance_after DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    description TEXT,
    reference_type ENUM('order', 'deposit', 'transfer', 'admin', 'refund') DEFAULT NULL,
    reference_id VARCHAR(50) DEFAULT NULL COMMENT 'ID of related order, transfer, etc.',
    payment_method VARCHAR(50) DEFAULT NULL COMMENT 'Payment method used',
    external_transaction_id VARCHAR(100) DEFAULT NULL COMMENT 'External payment system transaction ID',
    admin_user_id INT DEFAULT NULL COMMENT 'Admin user who performed the action',
    ip_address VARCHAR(45) DEFAULT NULL,
    metadata JSON DEFAULT NULL COMMENT 'Additional transaction data',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Orders table for service orders with pricing
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    services JSON NOT NULL,
    notes TEXT,
    budget_range VARCHAR(50),
    estimated_price DECIMAL(10,2) DEFAULT NULL,
    final_price DECIMAL(10,2) DEFAULT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'processing', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'partially_paid', 'refunded', 'failed') DEFAULT 'pending',
    payment_method VARCHAR(50) DEFAULT NULL,
    payment_reference VARCHAR(100) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
);

-- Affiliate System Tables
CREATE TABLE IF NOT EXISTS affiliate_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    affiliate_code VARCHAR(20) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    total_referrals INT DEFAULT 0,
    total_commissions DECIMAL(10,2) DEFAULT 0.00,
    total_withdrawn DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_affiliate_code (affiliate_code),
    INDEX idx_user_id (user_id),
    INDEX idx_active (is_active)
);

CREATE TABLE IF NOT EXISTS affiliate_referrals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL COMMENT 'User who made the referral',
    referred_id INT NOT NULL COMMENT 'User who was referred',
    affiliate_code VARCHAR(20) NOT NULL,
    referral_source VARCHAR(100) DEFAULT NULL COMMENT 'Where the referral came from',
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'pending_commission') DEFAULT 'pending',
    confirmed_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (affiliate_code) REFERENCES affiliate_codes(affiliate_code) ON DELETE CASCADE,
    UNIQUE KEY unique_referral (referred_id), -- Each user can only be referred once
    INDEX idx_referrer (referrer_id),
    INDEX idx_referred (referred_id),
    INDEX idx_affiliate_code (affiliate_code),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS affiliate_commissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    order_id INT DEFAULT NULL COMMENT 'Related order that triggered commission',
    pc_order_id INT DEFAULT NULL COMMENT 'Related PC order that triggered commission',
    commission_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    commission_type ENUM('referral_bonus', 'order_commission', 'special_bonus') DEFAULT 'referral_bonus',
    status ENUM('pending', 'paid', 'cancelled', 'pending_admin_review', 'approved', 'rejected') DEFAULT 'pending',
    transaction_id VARCHAR(50) DEFAULT NULL COMMENT 'Related credit transaction ID',
    approved_by INT DEFAULT NULL COMMENT 'Admin who approved/rejected the commission',
    admin_notes TEXT DEFAULT NULL COMMENT 'Admin notes for approval/rejection',
    notes TEXT DEFAULT NULL,
    paid_at TIMESTAMP NULL DEFAULT NULL,
    approved_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
    FOREIGN KEY (pc_order_id) REFERENCES pc_orders(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_referrer (referrer_id),
    INDEX idx_referred (referred_id),
    INDEX idx_status (status),
    INDEX idx_commission_type (commission_type),
    INDEX idx_created_at (created_at)
);

-- Affiliate Transfers Table - Tracks commission transfers to KMS Credit
CREATE TABLE IF NOT EXISTS affiliate_transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    transfer_type ENUM('commission_to_kms', 'kms_to_commission') DEFAULT 'commission_to_kms',
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    transaction_id VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Payment Transactions Table - Tracks all payment processor transactions
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('paypal', 'stripe', 'square', 'venmo', 'zelle', 'debit', 'credit_card') NOT NULL,
    external_transaction_id VARCHAR(255) NOT NULL,
    payer_id VARCHAR(255) NULL,
    status ENUM('pending', 'completed', 'failed', 'cancelled', 'pending_manual') DEFAULT 'pending',
    transaction_type ENUM('deposit', 'withdrawal') DEFAULT 'deposit',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_external_id (external_transaction_id),
    INDEX idx_created_at (created_at)
);

-- Chat System Tables
CREATE TABLE IF NOT EXISTS chat_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    admin_id INT,
    status ENUM('waiting', 'active', 'closed') DEFAULT 'waiting',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    closed_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS chat_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL,
    sender_id INT NOT NULL,
    message_type ENUM('text', 'image', 'file', 'system') DEFAULT 'text',
    message TEXT NOT NULL,
    attachment_url VARCHAR(255) DEFAULT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_created_at (created_at)
);

-- System Settings Table
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT DEFAULT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_setting_key (setting_key)
);

-- View counter table for tracking page views
CREATE TABLE IF NOT EXISTS view_counter (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_name VARCHAR(100) DEFAULT 'index',
    view_count INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_page (page_name)
);

-- Service Prices Table - Stores dynamic pricing for all services
CREATE TABLE IF NOT EXISTS service_prices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_category ENUM('optimize', 'print') NOT NULL COMMENT 'Service category: optimize for Photo/Video, print for Print Service',
    service_type VARCHAR(100) NOT NULL COMMENT 'Specific service type like photo_optimize, video_watermark, etc.',
    item_name VARCHAR(200) NOT NULL COMMENT 'Display name of the service item',
    item_name_en VARCHAR(200) NOT NULL COMMENT 'English name of the service item',
    item_name_zh VARCHAR(200) NOT NULL COMMENT 'Chinese name of the service item',
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Base price for the service',
    unit VARCHAR(50) DEFAULT 'each' COMMENT 'Unit of measurement (each, per minute, per page, etc.)',
    description TEXT COMMENT 'Detailed description of the service',
    description_en TEXT COMMENT 'English description',
    description_zh TEXT COMMENT 'Chinese description',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether this service is currently available',
    sort_order INT DEFAULT 0 COMMENT 'Display order',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (service_category),
    INDEX idx_type (service_type),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);

-- Initialize view counter for index page
INSERT INTO view_counter (page_name, view_count) VALUES ('index', 0)
ON DUPLICATE KEY UPDATE view_count = view_count;

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'System', 'Administrator', TRUE, TRUE, TRUE)
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    is_active = VALUES(is_active),
    is_admin = VALUES(is_admin);

-- Insert initial wallet for admin
INSERT INTO user_wallets (user_id, balance) 
SELECT 1, 1000.00 FROM DUAL 
WHERE NOT EXISTS (SELECT 1 FROM user_wallets WHERE user_id = 1);

-- Insert initial affiliate code for admin
INSERT INTO affiliate_codes (user_id, affiliate_code, is_active)
SELECT 1, 'ADMIN123', TRUE FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM affiliate_codes WHERE user_id = 1);

-- Insert initial service prices for Optimize Photo & Video
INSERT INTO service_prices (service_category, service_type, item_name, item_name_en, item_name_zh, base_price, unit, description, description_en, description_zh, sort_order) VALUES
('optimize', 'photo_optimize', 'Photo Optimization', 'Photo Optimization', '照片優化', 3.00, 'each', 'Optimize your low quality photo to the best quality', 'Optimize your low quality photo to the best quality', '將您的低質量照片優化到最佳質量', 1),
('optimize', 'photo_watermark', 'Photo Watermark Removal', 'Photo Watermark Removal', '照片去水印', 3.00, 'each', 'Remove watermark from photo', 'Remove watermark from photo', '去除照片水印', 2),
('optimize', 'video_optimize', 'Video Optimization (1 min 30FPS 1080P)', 'Video Optimization (1 min 30FPS 1080P)', '視頻優化 (1分鐘30FPS 1080P)', 2.00, 'per minute', 'Optimize video quality', 'Optimize video quality', '優化視頻質量', 3),
('optimize', 'video_watermark_easy', 'Video Watermark Removal - Easy Mode', 'Video Watermark Removal - Easy Mode', '視頻去水印 - 簡單模式', 10.00, 'up to 30 mins', 'Easy mode video watermark removal', 'Easy mode video watermark removal', '簡單模式視頻去水印', 4),
('optimize', 'video_watermark_hard', 'Video Watermark Removal - Hard Mode', 'Video Watermark Removal - Hard Mode', '視頻去水印 - 困難模式', 30.00, 'up to 30 mins', 'Hard mode video watermark removal', 'Hard mode video watermark removal', '困難模式視頻去水印', 5);

-- Insert initial service prices for Print Service
INSERT INTO service_prices (service_category, service_type, item_name, item_name_en, item_name_zh, base_price, unit, description, description_en, description_zh, sort_order) VALUES
('print', 'paper_32lb_black', '32 lb Premium Paper - Black', '32 lb Premium Paper - Black', '32磅高級紙 - 黑白', 0.50, 'per page', 'Letter size 32 lb premium paper in black', 'Letter size 32 lb premium paper in black', '信紙尺寸32磅高級紙黑白印刷', 1),
('print', 'paper_32lb_color', '32 lb Premium Paper - Color', '32 lb Premium Paper - Color', '32磅高級紙 - 彩色', 1.00, 'per page', 'Letter size 32 lb premium paper in color', 'Letter size 32 lb premium paper in color', '信紙尺寸32磅高級紙彩色印刷', 2),
('print', 'paper_110lb_black', '110 lb Ultra Premium Paper - Black', '110 lb Ultra Premium Paper - Black', '110磅超高級紙 - 黑白', 1.00, 'per page', 'Letter size 110 lb ultra premium paper in black', 'Letter size 110 lb ultra premium paper in black', '信紙尺寸110磅超高級紙黑白印刷', 3),
('print', 'paper_110lb_color', '110 lb Ultra Premium Paper - Color', '110 lb Ultra Premium Paper - Color', '110磅超高級紙 - 彩色', 2.00, 'per page', 'Letter size 110 lb ultra premium paper in color', 'Letter size 110 lb ultra premium paper in color', '信紙尺寸110磅超高級紙彩色印刷', 4),
('print', 'photo_45lb_black', '45 lbs Photo Paper (Matte) - Black', '45 lbs Photo Paper (Matte) - Black', '45磅照片紙(哑光) - 黑白', 1.50, 'per page', 'Letter size premium 45 lbs photo paper matte in black', 'Letter size premium 45 lbs photo paper matte in black', '信紙尺寸高級45磅照片紙哑光黑白印刷', 5),
('print', 'photo_45lb_color', '45 lbs Photo Paper (Matte) - Color', '45 lbs Photo Paper (Matte) - Color', '45磅照片紙(哑光) - 彩色', 3.00, 'per page', 'Letter size premium 45 lbs photo paper matte in color', 'Letter size premium 45 lbs photo paper matte in color', '信紙尺寸高級45磅照片紙哑光彩色印刷', 6),
('print', 'photo_70lb_black', '70 lbs Photo Paper (Glossy) - Black', '70 lbs Photo Paper (Glossy) - Black', '70磅照片紙(光面) - 黑白', 2.50, 'per page', 'Letter size five stars premium 70 lbs photo paper glossy in black', 'Letter size five stars premium 70 lbs photo paper glossy in black', '信紙尺寸五星級高級70磅照片紙光面黑白印刷', 7),
('print', 'photo_70lb_color', '70 lbs Photo Paper (Glossy) - Color', '70 lbs Photo Paper (Glossy) - Color', '70磅照片紙(光面) - 彩色', 5.00, 'per page', 'Letter size five stars premium 70 lbs photo paper glossy in color', 'Letter size five stars premium 70 lbs photo paper glossy in color', '信紙尺寸五星級高級70磅照片紙光面彩色印刷', 8),
('print', 'laminating_letter', '5 mil Thermal Laminating Pouch (Letter)', '5 mil Thermal Laminating Pouch (Letter)', '5密耳熱裱膜(信紙尺寸)', 5.00, 'each', 'Letter size 5 mil thermal laminating pouch', 'Letter size 5 mil thermal laminating pouch', '信紙尺寸5密耳熱裱膜', 9),
('print', 'photo_4x6', '4"x6" Photo Paper', '4"x6" Photo Paper', '4"x6"照片紙', 1.00, 'each', '4"x6" five stars premium 70 lbs photo paper', '4"x6" five stars premium 70 lbs photo paper', '4"x6"五星級高級70磅照片紙', 10),
('print', 'laminating_4x6', '4"x6" Laminating Pouch', '4"x6" Laminating Pouch', '4"x6"裱膜', 3.00, 'each', '4"x6" 5 mil thermal laminating pouch', '4"x6" 5 mil thermal laminating pouch', '4"x6" 5密耳熱裱膜', 11),
('print', 'album_basic', 'Basic Photo Album (36 Photos)', 'Basic Photo Album (36 Photos)', '基礎相册(36張照片)', 5.00, 'each', 'Basic photo album for 36 photos', 'Basic photo album for 36 photos', '基礎相册可放36張照片', 12),
('print', 'album_premium_52', 'Premium Hard Cover Album (52 Photos)', 'Premium Hard Cover Album (52 Photos)', '高級精裝相册(52張照片)', 15.00, 'each', 'Premium hard cover pink flower album for 52 photos', 'Premium hard cover pink flower album for 52 photos', '高級精裝粉色花朵相册可放52張照片', 13),
('print', 'album_premium_300', 'Premium Hard Cover Album (300 Photos)', 'Premium Hard Cover Album (300 Photos)', '高級精裝相册(300張照片)', 35.00, 'each', 'Premium hard cover pink flower album for 300 photos', 'Premium hard cover pink flower album for 300 photos', '高級精裝粉色花朵相册可放300張照片', 14),
('print', 'album_premium_600', 'Premium Hard Cover Album (600 Photos)', 'Premium Hard Cover Album (600 Photos)', '高級精裝相册(600張照片)', 55.00, 'each', 'Premium hard cover pink flower album for 600 photos', 'Premium hard cover pink flower album for 600 photos', '高級精裝粉色花朵相册可放600張照片', 15);

-- Insert essential system settings
INSERT INTO system_settings (setting_key, setting_value, description, is_public) VALUES
('site_name', 'KelvinKMS', 'The name of the website', TRUE),
('site_email', '<EMAIL>', 'Default email address for system emails', TRUE),
('default_currency', 'USD', 'Default currency for the system', TRUE),
('referral_bonus', '50.00', 'Default referral bonus amount', FALSE),
('min_withdrawal', '100.00', 'Minimum withdrawal amount', TRUE),
('max_file_upload_size', '5242880', 'Maximum file upload size in bytes (5MB)', TRUE)
ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    description = VALUES(description),
    is_public = VALUES(is_public);

-- Create necessary database users and permissions (adjust passwords in production)
-- Note: These commands are commented out for security - uncomment and modify as needed
-- CREATE USER IF NOT EXISTS 'kelvinkms_user'@'localhost' IDENTIFIED BY 'strong_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON kelvinkms.* TO 'kelvinkms_user'@'localhost';
-- FLUSH PRIVILEGES;

-- PC Component Categories Table
CREATE TABLE IF NOT EXISTS pc_component_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    category_name_en VARCHAR(100) NOT NULL,
    category_name_zh VARCHAR(100) NOT NULL,
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);

-- PC Components Table
CREATE TABLE IF NOT EXISTS pc_components (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    component_name VARCHAR(200) NOT NULL,
    component_name_en VARCHAR(200) NOT NULL,
    component_name_zh VARCHAR(200) NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    specifications JSON,
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    stock_quantity INT DEFAULT 0,
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES pc_component_categories(id) ON DELETE CASCADE,
    INDEX idx_category (category_id),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order),
    INDEX idx_price (current_price)
);

-- PC Prebuilt Configurations Table
CREATE TABLE IF NOT EXISTS pc_prebuilt_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_name VARCHAR(200) NOT NULL,
    config_name_en VARCHAR(200) NOT NULL,
    config_name_zh VARCHAR(200) NOT NULL,
    tier ENUM('budget', 'mid', 'high', 'premium') DEFAULT 'mid',
    primary_use ENUM('gaming', 'work', 'content_creation', 'general') DEFAULT 'general',
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    components JSON NOT NULL COMMENT 'JSON array of component IDs and quantities',
    specifications_summary JSON COMMENT 'Summary of key specifications',
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order),
    INDEX idx_tier (tier),
    INDEX idx_use (primary_use),
    INDEX idx_price (current_price)
);

-- PC Base Pricing Configuration Table
CREATE TABLE IF NOT EXISTS pc_base_pricing (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pricing_type ENUM('base_price', 'case_price') NOT NULL,
    config_key VARCHAR(50) NOT NULL COMMENT 'For base_price: "detailed_mode", for case_price: "small", "medium", "large"',
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    description TEXT COMMENT 'What is included in this price',
    description_en TEXT,
    description_zh TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_pricing (pricing_type, config_key),
    INDEX idx_type (pricing_type),
    INDEX idx_active (is_active)
);

-- Insert default base pricing configurations
INSERT INTO pc_base_pricing (pricing_type, config_key, price, description, description_en, description_zh, is_active) VALUES
('base_price', 'detailed_mode', 0.00, 'Base configuration includes: 10 premium RGB fans, premium RGB power cables, CPU special cooling treatment, premium CPU liquid cooler, GPU Power Adapter, RGB and Fan controller', 'Base configuration includes: 10 premium RGB fans, premium RGB power cables, CPU special cooling treatment, premium CPU liquid cooler, GPU Power Adapter, RGB and Fan controller', '基礎配置包含：十把頂級RGB風扇，頂級RGB電源線，CPU特殊散熱處理，高級CPU水冷散熱器，GPU Power Adapter，RGB and Fan controller', TRUE),
('case_price', 'small', 200.00, 'Small case - Premium quality with excellent aesthetics', 'Small case - Premium quality with excellent aesthetics', '小型機殼 - 頂級品質，外觀精美', TRUE),
('case_price', 'medium', 200.00, 'Medium case - Premium quality with excellent aesthetics', 'Medium case - Premium quality with excellent aesthetics', '中型機殼 - 頂級品質，外觀精美', TRUE),
('case_price', 'large', 300.00, 'Large case - Premium quality with excellent aesthetics', 'Large case - Premium quality with excellent aesthetics', '大型機殼 - 頂級品質，外觀精美', TRUE)
ON DUPLICATE KEY UPDATE
    price = VALUES(price),
    description = VALUES(description),
    description_en = VALUES(description_en),
    description_zh = VALUES(description_zh),
    is_active = VALUES(is_active);

-- PC Orders Table
CREATE TABLE IF NOT EXISTS pc_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_type ENUM('custom', 'prebuilt') NOT NULL DEFAULT 'custom',
    config_id INT NULL COMMENT 'Reference to prebuilt config if order_type is prebuilt',
    components JSON COMMENT 'Selected components and quantities',
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    shipping_address TEXT,
    notes TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (config_id) REFERENCES pc_prebuilt_configs(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created (created_at)
);

-- Service Prices Table
CREATE TABLE IF NOT EXISTS service_prices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_category ENUM('optimize', 'print') NOT NULL COMMENT 'Service category: optimize for Photo/Video, print for Print Service',
    service_type VARCHAR(100) NOT NULL COMMENT 'Specific service type like photo_optimize, video_watermark, etc.',
    item_name VARCHAR(200) NOT NULL COMMENT 'Display name of the service item',
    item_name_en VARCHAR(200) NOT NULL COMMENT 'English name of the service item',
    item_name_zh VARCHAR(200) NOT NULL COMMENT 'Chinese name of the service item',
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Base price for the service',
    unit VARCHAR(50) DEFAULT 'each' COMMENT 'Unit of measurement (each, per minute, per page, etc.)',
    description TEXT COMMENT 'Detailed description of the service',
    description_en TEXT COMMENT 'English description',
    description_zh TEXT COMMENT 'Chinese description',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether this service is currently available',
    sort_order INT DEFAULT 0 COMMENT 'Display order',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (service_category),
    INDEX idx_type (service_type),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);

-- Create database indexes for better performance
-- Insert initial service prices for Optimize Photo & Video
INSERT INTO service_prices (service_category, service_type, item_name, item_name_en, item_name_zh, base_price, unit, description, description_en, description_zh, sort_order) VALUES
('optimize', 'photo_optimize', 'Photo Optimization', 'Photo Optimization', '照片優化', 3.00, 'each', 'Optimize your low quality photo to the best quality', 'Optimize your low quality photo to the best quality', '將您的低質量照片優化到最佳質量', 1),
('optimize', 'photo_watermark', 'Photo Watermark Removal', 'Photo Watermark Removal', '照片去水印', 3.00, 'each', 'Remove watermark from photo', 'Remove watermark from photo', '去除照片水印', 2),
('optimize', 'video_optimize', 'Video Optimization (1 min 30FPS 1080P)', 'Video Optimization (1 min 30FPS 1080P)', '視頻優化 (1分鐘30FPS 1080P)', 2.00, 'per minute', 'Optimize video quality', 'Optimize video quality', '優化視頻質量', 3),
('optimize', 'video_watermark_easy', 'Video Watermark Removal - Easy Mode', 'Video Watermark Removal - Easy Mode', '視頻去水印 - 簡單模式', 10.00, 'up to 30 mins', 'Easy mode video watermark removal', 'Easy mode video watermark removal', '簡單模式視頻去水印', 4),
('optimize', 'video_watermark_hard', 'Video Watermark Removal - Hard Mode', 'Video Watermark Removal - Hard Mode', '視頻去水印 - 困難模式', 30.00, 'up to 30 mins', 'Hard mode video watermark removal', 'Hard mode video watermark removal', '困難模式視頻去水印', 5);

-- Insert initial service prices for Print Service
INSERT INTO service_prices (service_category, service_type, item_name, item_name_en, item_name_zh, base_price, unit, description, description_en, description_zh, sort_order) VALUES
('print', 'paper_32lb_black', '32 lb Premium Paper - Black', '32 lb Premium Paper - Black', '32磅高級紙 - 黑白', 0.50, 'per page', 'Letter size 32 lb premium paper in black', 'Letter size 32 lb premium paper in black', '信紙尺寸32磅高級紙黑白印刷', 1),
('print', 'paper_32lb_color', '32 lb Premium Paper - Color', '32 lb Premium Paper - Color', '32磅高級紙 - 彩色', 1.00, 'per page', 'Letter size 32 lb premium paper in color', 'Letter size 32 lb premium paper in color', '信紙尺寸32磅高級紙彩色印刷', 2),
('print', 'paper_110lb_black', '110 lb Ultra Premium Paper - Black', '110 lb Ultra Premium Paper - Black', '110磅超高級紙 - 黑白', 1.00, 'per page', 'Letter size 110 lb ultra premium paper in black', 'Letter size 110 lb ultra premium paper in black', '信紙尺寸110磅超高級紙黑白印刷', 3),
('print', 'paper_110lb_color', '110 lb Ultra Premium Paper - Color', '110 lb Ultra Premium Paper - Color', '110磅超高級紙 - 彩色', 2.00, 'per page', 'Letter size 110 lb ultra premium paper in color', 'Letter size 110 lb ultra premium paper in color', '信紙尺寸110磅超高級紙彩色印刷', 4),
('print', 'photo_45lb_black', '45 lbs Photo Paper (Matte) - Black', '45 lbs Photo Paper (Matte) - Black', '45磅照片紙(哑光) - 黑白', 1.50, 'per page', 'Letter size premium 45 lbs photo paper matte in black', 'Letter size premium 45 lbs photo paper matte in black', '信紙尺寸高級45磅照片紙哑光黑白印刷', 5),
('print', 'photo_45lb_color', '45 lbs Photo Paper (Matte) - Color', '45 lbs Photo Paper (Matte) - Color', '45磅照片紙(哑光) - 彩色', 3.00, 'per page', 'Letter size premium 45 lbs photo paper matte in color', 'Letter size premium 45 lbs photo paper matte in color', '信紙尺寸高級45磅照片紙哑光彩色印刷', 6),
('print', 'photo_70lb_black', '70 lbs Photo Paper (Glossy) - Black', '70 lbs Photo Paper (Glossy) - Black', '70磅照片紙(光面) - 黑白', 2.50, 'per page', 'Letter size five stars premium 70 lbs photo paper glossy in black', 'Letter size five stars premium 70 lbs photo paper glossy in black', '信紙尺寸五星級高級70磅照片紙光面黑白印刷', 7),
('print', 'photo_70lb_color', '70 lbs Photo Paper (Glossy) - Color', '70 lbs Photo Paper (Glossy) - Color', '70磅照片紙(光面) - 彩色', 5.00, 'per page', 'Letter size five stars premium 70 lbs photo paper glossy in color', 'Letter size five stars premium 70 lbs photo paper glossy in color', '信紙尺寸五星級高級70磅照片紙光面彩色印刷', 8),
('print', 'laminating_letter', '5 mil Thermal Laminating Pouch (Letter)', '5 mil Thermal Laminating Pouch (Letter)', '5密耳熱裱膜(信紙尺寸)', 5.00, 'each', 'Letter size 5 mil thermal laminating pouch', 'Letter size 5 mil thermal laminating pouch', '信紙尺寸5密耳熱裱膜', 9),
('print', 'photo_4x6', '4"x6" Photo Paper', '4"x6" Photo Paper', '4"x6"照片紙', 1.00, 'each', '4"x6" five stars premium 70 lbs photo paper', '4"x6" five stars premium 70 lbs photo paper', '4"x6"五星級高級70磅照片紙', 10),
('print', 'laminating_4x6', '4"x6" Laminating Pouch', '4"x6" Laminating Pouch', '4"x6"裱膜', 3.00, 'each', '4"x6" 5 mil thermal laminating pouch', '4"x6" 5 mil thermal laminating pouch', '4"x6" 5密耳熱裱膜', 11),
('print', 'album_4x6_20', '4"x6" Photo Album (20 pages)', '4"x6" Photo Album (20 pages)', '4"x6"相冊 (20頁)', 15.00, 'each', '4"x6" photo album with 20 pages', '4"x6" photo album with 20 pages', '4"x6"相冊，20頁', 12),
('print', 'album_4x6_40', '4"x6" Photo Album (40 pages)', '4"x6" Photo Album (40 pages)', '4"x6"相冊 (40頁)', 25.00, 'each', '4"x6" photo album with 40 pages', '4"x6" photo album with 40 pages', '4"x6"相冊，40頁', 13),
('print', 'album_5x7_20', '5"x7" Photo Album (20 pages)', '5"x7" Photo Album (20 pages)', '5"x7"相冊 (20頁)', 20.00, 'each', '5"x7" photo album with 20 pages', '5"x7" photo album with 20 pages', '5"x7"相冊，20頁', 14),
('print', 'album_5x7_40', '5"x7" Photo Album (40 pages)', '5"x7" Photo Album (40 pages)', '5"x7"相冊 (40頁)', 35.00, 'each', '5"x7" photo album with 40 pages', '5"x7" photo album with 40 pages', '5"x7"相冊，40頁', 15)
ON DUPLICATE KEY UPDATE
    item_name = VALUES(item_name),
    item_name_en = VALUES(item_name_en),
    item_name_zh = VALUES(item_name_zh),
    base_price = VALUES(base_price),
    unit = VALUES(unit),
    description = VALUES(description),
    description_en = VALUES(description_en),
    description_zh = VALUES(description_zh),
    sort_order = VALUES(sort_order);

-- Create database indexes for better performance
CREATE INDEX IF NOT EXISTS idx_orders_user_id_status ON orders(user_id, status);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id_type ON credit_transactions(user_id, transaction_type);
CREATE INDEX IF NOT EXISTS idx_affiliate_commissions_referrer_status ON affiliate_commissions(referrer_id, status);
CREATE INDEX IF NOT EXISTS idx_chat_messages_session_created ON chat_messages(session_id, created_at);
