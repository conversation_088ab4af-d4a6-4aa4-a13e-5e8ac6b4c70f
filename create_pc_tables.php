<?php
// Create PC tables and insert sample data
require_once 'config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h2>Creating PC Tables</h2>";

try {
    $link = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
    
    if (!$link) {
        throw new Exception("Database connection failed: " . mysqli_connect_error());
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Create pc_component_categories table
    $sql = "CREATE TABLE IF NOT EXISTS pc_component_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_name VARCHAR(100) NOT NULL,
        category_name_en VARCHAR(100) NOT NULL,
        category_name_zh VARCHAR(100) NOT NULL,
        description TEXT,
        description_en TEXT,
        description_zh TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order)
    )";
    
    if (mysqli_query($link, $sql)) {
        echo "<p>✅ pc_component_categories table created</p>";
    } else {
        echo "<p>❌ Error creating pc_component_categories: " . mysqli_error($link) . "</p>";
    }
    
    // Insert sample categories
    $categories = [
        ['CPU', 'CPU', 'CPU', 'Central Processing Unit', 'Central Processing Unit', 'CPU', 1],
        ['GPU', 'GPU', 'GPU', 'Graphics Processing Unit', 'Graphics Processing Unit', 'GPU', 2],
        ['RAM', 'RAM', 'RAM', 'Memory', 'Memory', '内存', 3],
        ['Storage', 'Storage', 'Storage', 'Storage Devices', 'Storage Devices', '存储设备', 4],
        ['PSU', 'PSU', 'PSU', 'Power Supply Unit', 'Power Supply Unit', '电源', 5],
        ['Case', 'Case', 'Case', 'Computer Case', 'Computer Case', '机箱', 6],
        ['Motherboard', 'Motherboard', 'Motherboard', 'Motherboard', 'Motherboard', '主板', 7]
    ];
    
    foreach ($categories as $category) {
        $sql = "INSERT IGNORE INTO pc_component_categories (category_name, category_name_en, category_name_zh, description, description_en, description_zh, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($link, $sql);
        mysqli_stmt_bind_param($stmt, "ssssssi", $category[0], $category[1], $category[2], $category[3], $category[4], $category[5], $category[6]);
        if (mysqli_stmt_execute($stmt)) {
            echo "<p>✅ Category '{$category[0]}' inserted</p>";
        } else {
            echo "<p>⚠️ Category '{$category[0]}' may already exist</p>";
        }
        mysqli_stmt_close($stmt);
    }
    
    // Create pc_components table
    $sql = "CREATE TABLE IF NOT EXISTS pc_components (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_id INT NOT NULL,
        component_name VARCHAR(200) NOT NULL,
        component_name_en VARCHAR(200) NOT NULL,
        component_name_zh VARCHAR(200) NOT NULL,
        brand VARCHAR(100),
        model VARCHAR(100),
        specifications JSON,
        base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        stock_quantity INT DEFAULT 0,
        description TEXT,
        description_en TEXT,
        description_zh TEXT,
        image_url VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category_id),
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order),
        INDEX idx_price (current_price)
    )";
    
    if (mysqli_query($link, $sql)) {
        echo "<p>✅ pc_components table created</p>";
    } else {
        echo "<p>❌ Error creating pc_components: " . mysqli_error($link) . "</p>";
    }
    
    // Create pc_prebuilt_configs table
    $sql = "CREATE TABLE IF NOT EXISTS pc_prebuilt_configs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_name VARCHAR(200) NOT NULL,
        config_name_en VARCHAR(200) NOT NULL,
        config_name_zh VARCHAR(200) NOT NULL,
        tier ENUM('budget', 'mid', 'high', 'premium') DEFAULT 'mid',
        primary_use ENUM('gaming', 'work', 'content_creation', 'general') DEFAULT 'general',
        description TEXT,
        description_en TEXT,
        description_zh TEXT,
        components JSON NOT NULL COMMENT 'JSON array of component IDs and quantities',
        specifications_summary JSON COMMENT 'Summary of key specifications',
        base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        image_url VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order),
        INDEX idx_tier (tier),
        INDEX idx_use (primary_use),
        INDEX idx_price (current_price)
    )";
    
    if (mysqli_query($link, $sql)) {
        echo "<p>✅ pc_prebuilt_configs table created</p>";
    } else {
        echo "<p>❌ Error creating pc_prebuilt_configs: " . mysqli_error($link) . "</p>";
    }
    
    // Create pc_orders table
    $sql = "CREATE TABLE IF NOT EXISTS pc_orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        order_type ENUM('custom', 'prebuilt') NOT NULL DEFAULT 'custom',
        prebuilt_config_id INT NULL COMMENT 'Reference to prebuilt config if order_type is prebuilt',
        components JSON COMMENT 'Selected components and quantities',
        total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
        payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
        payment_method VARCHAR(50),
        shipping_address TEXT,
        notes TEXT,
        admin_notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user (user_id),
        INDEX idx_status (status),
        INDEX idx_payment_status (payment_status),
        INDEX idx_created (created_at)
    )";
    
    if (mysqli_query($link, $sql)) {
        echo "<p>✅ pc_orders table created</p>";
    } else {
        echo "<p>❌ Error creating pc_orders: " . mysqli_error($link) . "</p>";
    }
    
    mysqli_close($link);
    
    echo "<h2>Setup Complete!</h2>";
    echo "<p><a href='KMS_Apps/KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=get_categories'>Test Categories API</a></p>";
    echo "<p><a href='KMS_Apps/KMS_Admin/KMS_PCManagement/KMS_PHP/KMS_admin_pc_management.php'>Go to Admin PC Management</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
