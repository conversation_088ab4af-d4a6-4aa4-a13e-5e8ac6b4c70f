<?php
// Create service_prices table and insert sample data
require_once 'config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h2>Creating Service Prices Table</h2>";

try {
    $link = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
    
    if (!$link) {
        throw new Exception("Database connection failed: " . mysqli_connect_error());
    }
    
    echo "<p>✅ Database connection successful</p>";
    
    // Create service_prices table
    $sql = "CREATE TABLE IF NOT EXISTS service_prices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        service_category ENUM('optimize', 'print') NOT NULL COMMENT 'Service category: optimize for Photo/Video, print for Print Service',
        service_type VARCHAR(100) NOT NULL COMMENT 'Specific service type like photo_optimize, video_watermark, etc.',
        item_name VARCHAR(200) NOT NULL COMMENT 'Display name of the service item',
        item_name_en VARCHAR(200) NOT NULL COMMENT 'English name of the service item',
        item_name_zh VARCHAR(200) NOT NULL COMMENT 'Chinese name of the service item',
        base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Base price for the service',
        unit VARCHAR(50) DEFAULT 'each' COMMENT 'Unit of measurement (each, per minute, per page, etc.)',
        description TEXT COMMENT 'Detailed description of the service',
        description_en TEXT COMMENT 'English description',
        description_zh TEXT COMMENT 'Chinese description',
        is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether this service is currently available',
        sort_order INT DEFAULT 0 COMMENT 'Display order',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (service_category),
        INDEX idx_type (service_type),
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order)
    )";
    
    if (mysqli_query($link, $sql)) {
        echo "<p>✅ service_prices table created</p>";
    } else {
        echo "<p>❌ Error creating service_prices: " . mysqli_error($link) . "</p>";
    }
    
    // Insert optimize service prices
    $optimize_services = [
        ['optimize', 'photo_optimize', 'Photo Optimization', 'Photo Optimization', '照片優化', 3.00, 'each', 'Optimize your low quality photo to the best quality', 'Optimize your low quality photo to the best quality', '將您的低質量照片優化到最佳質量', 1],
        ['optimize', 'photo_watermark', 'Photo Watermark Removal', 'Photo Watermark Removal', '照片去水印', 3.00, 'each', 'Remove watermark from photo', 'Remove watermark from photo', '去除照片水印', 2],
        ['optimize', 'video_optimize', 'Video Optimization (1 min 30FPS 1080P)', 'Video Optimization (1 min 30FPS 1080P)', '視頻優化 (1分鐘30FPS 1080P)', 2.00, 'per minute', 'Optimize video quality', 'Optimize video quality', '優化視頻質量', 3],
        ['optimize', 'video_watermark_easy', 'Video Watermark Removal - Easy Mode', 'Video Watermark Removal - Easy Mode', '視頻去水印 - 簡單模式', 10.00, 'up to 30 mins', 'Easy mode video watermark removal', 'Easy mode video watermark removal', '簡單模式視頻去水印', 4],
        ['optimize', 'video_watermark_hard', 'Video Watermark Removal - Hard Mode', 'Video Watermark Removal - Hard Mode', '視頻去水印 - 困難模式', 30.00, 'up to 30 mins', 'Hard mode video watermark removal', 'Hard mode video watermark removal', '困難模式視頻去水印', 5]
    ];
    
    foreach ($optimize_services as $service) {
        $sql = "INSERT IGNORE INTO service_prices (service_category, service_type, item_name, item_name_en, item_name_zh, base_price, unit, description, description_en, description_zh, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($link, $sql);
        mysqli_stmt_bind_param($stmt, "sssssdssssi", $service[0], $service[1], $service[2], $service[3], $service[4], $service[5], $service[6], $service[7], $service[8], $service[9], $service[10]);
        if (mysqli_stmt_execute($stmt)) {
            echo "<p>✅ Service '{$service[2]}' inserted</p>";
        } else {
            echo "<p>⚠️ Service '{$service[2]}' may already exist</p>";
        }
        mysqli_stmt_close($stmt);
    }
    
    // Insert print service prices
    $print_services = [
        ['print', 'paper_32lb_black', '32 lb Premium Paper - Black', '32 lb Premium Paper - Black', '32磅高級紙 - 黑白', 0.50, 'per page', 'Letter size 32 lb premium paper in black', 'Letter size 32 lb premium paper in black', '信紙尺寸32磅高級紙黑白印刷', 1],
        ['print', 'paper_32lb_color', '32 lb Premium Paper - Color', '32 lb Premium Paper - Color', '32磅高級紙 - 彩色', 1.00, 'per page', 'Letter size 32 lb premium paper in color', 'Letter size 32 lb premium paper in color', '信紙尺寸32磅高級紙彩色印刷', 2],
        ['print', 'paper_110lb_black', '110 lb Ultra Premium Paper - Black', '110 lb Ultra Premium Paper - Black', '110磅超高級紙 - 黑白', 1.00, 'per page', 'Letter size 110 lb ultra premium paper in black', 'Letter size 110 lb ultra premium paper in black', '信紙尺寸110磅超高級紙黑白印刷', 3],
        ['print', 'paper_110lb_color', '110 lb Ultra Premium Paper - Color', '110 lb Ultra Premium Paper - Color', '110磅超高級紙 - 彩色', 2.00, 'per page', 'Letter size 110 lb ultra premium paper in color', 'Letter size 110 lb ultra premium paper in color', '信紙尺寸110磅超高級紙彩色印刷', 4],
        ['print', 'photo_45lb_black', '45 lbs Photo Paper (Matte) - Black', '45 lbs Photo Paper (Matte) - Black', '45磅照片紙(哑光) - 黑白', 1.50, 'per page', 'Letter size premium 45 lbs photo paper matte in black', 'Letter size premium 45 lbs photo paper matte in black', '信紙尺寸高級45磅照片紙哑光黑白印刷', 5],
        ['print', 'photo_45lb_color', '45 lbs Photo Paper (Matte) - Color', '45 lbs Photo Paper (Matte) - Color', '45磅照片紙(哑光) - 彩色', 3.00, 'per page', 'Letter size premium 45 lbs photo paper matte in color', 'Letter size premium 45 lbs photo paper matte in color', '信紙尺寸高級45磅照片紙哑光彩色印刷', 6],
        ['print', 'photo_70lb_black', '70 lbs Photo Paper (Glossy) - Black', '70 lbs Photo Paper (Glossy) - Black', '70磅照片紙(光面) - 黑白', 2.50, 'per page', 'Letter size five stars premium 70 lbs photo paper glossy in black', 'Letter size five stars premium 70 lbs photo paper glossy in black', '信紙尺寸五星級高級70磅照片紙光面黑白印刷', 7],
        ['print', 'photo_70lb_color', '70 lbs Photo Paper (Glossy) - Color', '70 lbs Photo Paper (Glossy) - Color', '70磅照片紙(光面) - 彩色', 5.00, 'per page', 'Letter size five stars premium 70 lbs photo paper glossy in color', 'Letter size five stars premium 70 lbs photo paper glossy in color', '信紙尺寸五星級高級70磅照片紙光面彩色印刷', 8],
        ['print', 'laminating_letter', '5 mil Thermal Laminating Pouch (Letter)', '5 mil Thermal Laminating Pouch (Letter)', '5密耳熱裱膜(信紙尺寸)', 5.00, 'each', 'Letter size 5 mil thermal laminating pouch', 'Letter size 5 mil thermal laminating pouch', '信紙尺寸5密耳熱裱膜', 9],
        ['print', 'photo_4x6', '4"x6" Photo Paper', '4"x6" Photo Paper', '4"x6"照片紙', 1.00, 'each', '4"x6" five stars premium 70 lbs photo paper', '4"x6" five stars premium 70 lbs photo paper', '4"x6"五星級高級70磅照片紙', 10],
        ['print', 'laminating_4x6', '4"x6" Laminating Pouch', '4"x6" Laminating Pouch', '4"x6"裱膜', 3.00, 'each', '4"x6" 5 mil thermal laminating pouch', '4"x6" 5 mil thermal laminating pouch', '4"x6" 5密耳熱裱膜', 11],
        ['print', 'album_4x6_20', '4"x6" Photo Album (20 pages)', '4"x6" Photo Album (20 pages)', '4"x6"相冊 (20頁)', 15.00, 'each', '4"x6" photo album with 20 pages', '4"x6" photo album with 20 pages', '4"x6"相冊，20頁', 12],
        ['print', 'album_4x6_40', '4"x6" Photo Album (40 pages)', '4"x6" Photo Album (40 pages)', '4"x6"相冊 (40頁)', 25.00, 'each', '4"x6" photo album with 40 pages', '4"x6" photo album with 40 pages', '4"x6"相冊，40頁', 13],
        ['print', 'album_5x7_20', '5"x7" Photo Album (20 pages)', '5"x7" Photo Album (20 pages)', '5"x7"相冊 (20頁)', 20.00, 'each', '5"x7" photo album with 20 pages', '5"x7" photo album with 20 pages', '5"x7"相冊，20頁', 14],
        ['print', 'album_5x7_40', '5"x7" Photo Album (40 pages)', '5"x7" Photo Album (40 pages)', '5"x7"相冊 (40頁)', 35.00, 'each', '5"x7" photo album with 40 pages', '5"x7" photo album with 40 pages', '5"x7"相冊，40頁', 15]
    ];
    
    foreach ($print_services as $service) {
        $sql = "INSERT IGNORE INTO service_prices (service_category, service_type, item_name, item_name_en, item_name_zh, base_price, unit, description, description_en, description_zh, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($link, $sql);
        mysqli_stmt_bind_param($stmt, "sssssdssssi", $service[0], $service[1], $service[2], $service[3], $service[4], $service[5], $service[6], $service[7], $service[8], $service[9], $service[10]);
        if (mysqli_stmt_execute($stmt)) {
            echo "<p>✅ Service '{$service[2]}' inserted</p>";
        } else {
            echo "<p>⚠️ Service '{$service[2]}' may already exist</p>";
        }
        mysqli_stmt_close($stmt);
    }
    
    mysqli_close($link);
    
    echo "<h2>Setup Complete!</h2>";
    echo "<p><a href='index.php'>Go to Homepage</a></p>";
    echo "<p><a href='KMS_Apps/KMS_Member/KMS_Orders/KMS_PHP/KMS_service_prices_api.php?action=get_prices'>Test Service Prices API</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
