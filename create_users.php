<?php
// Create admin and regular user accounts
require_once 'config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h2>創建用戶帳號</h2>";

try {
    $link = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
    
    if (!$link) {
        throw new Exception("Database connection failed: " . mysqli_connect_error());
    }
    
    echo "<p>✅ 數據庫連接成功</p>";
    
    // Hash the password k1e9l9v9in
    $password = 'k1e9l9v9in';
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    echo "<p>🔐 密碼已加密: " . substr($hashed_password, 0, 30) . "...</p>";
    
    // Create admin user
    $admin_sql = "INSERT INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin, language) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                  ON DUPLICATE KEY UPDATE 
                      password = VALUES(password),
                      is_active = VALUES(is_active),
                      is_admin = VALUES(is_admin)";
    
    $stmt = mysqli_prepare($link, $admin_sql);
    $admin_username = 'admin';
    $admin_email = '<EMAIL>';
    $admin_first_name = 'System';
    $admin_last_name = 'Administrator';
    $is_verified = 1;
    $is_active = 1;
    $is_admin = 1;
    $language = 'en';
    
    mysqli_stmt_bind_param($stmt, "sssssiiis", 
        $admin_username, $hashed_password, $admin_email, 
        $admin_first_name, $admin_last_name, 
        $is_verified, $is_active, $is_admin, $language
    );
    
    if (mysqli_stmt_execute($stmt)) {
        $admin_user_id = mysqli_insert_id($link);
        if ($admin_user_id == 0) {
            // User already exists, get the ID
            $get_id_sql = "SELECT id FROM users WHERE username = ?";
            $id_stmt = mysqli_prepare($link, $get_id_sql);
            mysqli_stmt_bind_param($id_stmt, "s", $admin_username);
            mysqli_stmt_execute($id_stmt);
            $result = mysqli_stmt_get_result($id_stmt);
            $row = mysqli_fetch_assoc($result);
            $admin_user_id = $row['id'];
            mysqli_stmt_close($id_stmt);
        }
        echo "<p>✅ 管理員帳號創建/更新成功 (ID: $admin_user_id)</p>";
        echo "<p>&nbsp;&nbsp;&nbsp;用戶名: admin</p>";
        echo "<p>&nbsp;&nbsp;&nbsp;密碼: k1e9l9v9in</p>";
        echo "<p>&nbsp;&nbsp;&nbsp;權限: 管理員</p>";
    } else {
        echo "<p>❌ 管理員帳號創建失敗: " . mysqli_stmt_error($stmt) . "</p>";
    }
    mysqli_stmt_close($stmt);
    
    // Create regular user
    $user_sql = "INSERT INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin, language) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                 ON DUPLICATE KEY UPDATE 
                     password = VALUES(password),
                     is_active = VALUES(is_active),
                     is_admin = VALUES(is_admin)";
    
    $stmt = mysqli_prepare($link, $user_sql);
    $user_username = 'KelvinKMS';
    $user_email = '<EMAIL>';
    $user_first_name = 'Kelvin';
    $user_last_name = 'KMS';
    $user_is_verified = 1;
    $user_is_active = 1;
    $user_is_admin = 0;
    $user_language = 'en';
    
    mysqli_stmt_bind_param($stmt, "sssssiiis", 
        $user_username, $hashed_password, $user_email, 
        $user_first_name, $user_last_name, 
        $user_is_verified, $user_is_active, $user_is_admin, $user_language
    );
    
    if (mysqli_stmt_execute($stmt)) {
        $regular_user_id = mysqli_insert_id($link);
        if ($regular_user_id == 0) {
            // User already exists, get the ID
            $get_id_sql = "SELECT id FROM users WHERE username = ?";
            $id_stmt = mysqli_prepare($link, $get_id_sql);
            mysqli_stmt_bind_param($id_stmt, "s", $user_username);
            mysqli_stmt_execute($id_stmt);
            $result = mysqli_stmt_get_result($id_stmt);
            $row = mysqli_fetch_assoc($result);
            $regular_user_id = $row['id'];
            mysqli_stmt_close($id_stmt);
        }
        echo "<p>✅ 普通用戶帳號創建/更新成功 (ID: $regular_user_id)</p>";
        echo "<p>&nbsp;&nbsp;&nbsp;用戶名: KelvinKMS</p>";
        echo "<p>&nbsp;&nbsp;&nbsp;密碼: k1e9l9v9in</p>";
        echo "<p>&nbsp;&nbsp;&nbsp;權限: 普通會員</p>";
    } else {
        echo "<p>❌ 普通用戶帳號創建失敗: " . mysqli_stmt_error($stmt) . "</p>";
    }
    mysqli_stmt_close($stmt);
    
    // Create wallets for both users if they don't exist
    echo "<h3>創建用戶錢包</h3>";
    
    // Admin wallet
    $wallet_sql = "INSERT IGNORE INTO user_wallets (user_id, balance) VALUES (?, 1000.00)";
    $stmt = mysqli_prepare($link, $wallet_sql);
    mysqli_stmt_bind_param($stmt, "i", $admin_user_id);
    if (mysqli_stmt_execute($stmt)) {
        echo "<p>✅ 管理員錢包創建成功 (初始餘額: $1000.00)</p>";
    } else {
        echo "<p>⚠️ 管理員錢包可能已存在</p>";
    }
    mysqli_stmt_close($stmt);
    
    // Regular user wallet
    $stmt = mysqli_prepare($link, $wallet_sql);
    $regular_balance = 100.00;
    mysqli_stmt_bind_param($stmt, "i", $regular_user_id);
    if (mysqli_stmt_execute($stmt)) {
        echo "<p>✅ 普通用戶錢包創建成功 (初始餘額: $100.00)</p>";
    } else {
        echo "<p>⚠️ 普通用戶錢包可能已存在</p>";
    }
    mysqli_stmt_close($stmt);
    
    // Create affiliate codes
    echo "<h3>創建推薦代碼</h3>";
    
    // Admin affiliate code
    $affiliate_sql = "INSERT IGNORE INTO affiliate_codes (user_id, affiliate_code, is_active) VALUES (?, ?, TRUE)";
    $stmt = mysqli_prepare($link, $affiliate_sql);
    $admin_code = 'ADMIN123';
    mysqli_stmt_bind_param($stmt, "is", $admin_user_id, $admin_code);
    if (mysqli_stmt_execute($stmt)) {
        echo "<p>✅ 管理員推薦代碼創建成功: ADMIN123</p>";
    } else {
        echo "<p>⚠️ 管理員推薦代碼可能已存在</p>";
    }
    mysqli_stmt_close($stmt);
    
    // Regular user affiliate code
    $stmt = mysqli_prepare($link, $affiliate_sql);
    $user_code = 'KELVIN123';
    mysqli_stmt_bind_param($stmt, "is", $regular_user_id, $user_code);
    if (mysqli_stmt_execute($stmt)) {
        echo "<p>✅ 普通用戶推薦代碼創建成功: KELVIN123</p>";
    } else {
        echo "<p>⚠️ 普通用戶推薦代碼可能已存在</p>";
    }
    mysqli_stmt_close($stmt);
    
    mysqli_close($link);
    
    echo "<h2>創建完成！</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>登錄信息：</h3>";
    echo "<p><strong>管理員帳號：</strong></p>";
    echo "<p>用戶名: <code>admin</code></p>";
    echo "<p>密碼: <code>k1e9l9v9in</code></p>";
    echo "<p>權限: 管理員</p>";
    echo "<br>";
    echo "<p><strong>普通會員帳號：</strong></p>";
    echo "<p>用戶名: <code>KelvinKMS</code></p>";
    echo "<p>密碼: <code>k1e9l9v9in</code></p>";
    echo "<p>權限: 普通會員</p>";
    echo "</div>";
    
    echo "<p><a href='index.php' style='background: #0066cc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>返回首頁</a></p>";
    echo "<p><a href='admin.php' style='background: #cc6600; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>進入管理後台</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ 錯誤: " . $e->getMessage() . "</p>";
}
?>
