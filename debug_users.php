<?php
// Debug users table issue
require_once 'KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h2>調試用戶表問題</h2>";

try {
    $link = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
    
    if (!$link) {
        throw new Exception("Database connection failed: " . mysqli_connect_error());
    }
    
    echo "<p>✅ 數據庫連接成功</p>";
    echo "<p>數據庫: " . DB_NAME . "</p>";
    echo "<p>服務器: " . DB_SERVER . "</p>";
    
    // Check if users table exists
    echo "<h3>檢查 users 表是否存在</h3>";
    $check_table = "SHOW TABLES LIKE 'users'";
    $result = mysqli_query($link, $check_table);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<p>✅ users 表存在</p>";
        
        // Show table structure
        echo "<h3>users 表結構</h3>";
        $describe = "DESCRIBE users";
        $desc_result = mysqli_query($link, $describe);
        
        if ($desc_result) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>字段</th>";
            echo "<th style='padding: 8px;'>類型</th>";
            echo "<th style='padding: 8px;'>空值</th>";
            echo "<th style='padding: 8px;'>鍵</th>";
            echo "<th style='padding: 8px;'>默認值</th>";
            echo "<th style='padding: 8px;'>額外</th>";
            echo "</tr>";
            
            while ($row = mysqli_fetch_assoc($desc_result)) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $row['Field'] . "</td>";
                echo "<td style='padding: 8px;'>" . $row['Type'] . "</td>";
                echo "<td style='padding: 8px;'>" . $row['Null'] . "</td>";
                echo "<td style='padding: 8px;'>" . $row['Key'] . "</td>";
                echo "<td style='padding: 8px;'>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 8px;'>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Count rows in users table
        echo "<h3>users 表記錄數</h3>";
        $count_sql = "SELECT COUNT(*) as count FROM users";
        $count_result = mysqli_query($link, $count_sql);
        
        if ($count_result) {
            $count_row = mysqli_fetch_assoc($count_result);
            echo "<p>當前記錄數: <strong>" . $count_row['count'] . "</strong></p>";
            
            if ($count_row['count'] > 0) {
                // Show existing users
                echo "<h3>現有用戶</h3>";
                $users_sql = "SELECT id, username, email, first_name, last_name, is_admin, is_active, created_at FROM users";
                $users_result = mysqli_query($link, $users_sql);
                
                if ($users_result) {
                    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                    echo "<tr style='background: #f0f0f0;'>";
                    echo "<th style='padding: 8px;'>ID</th>";
                    echo "<th style='padding: 8px;'>用戶名</th>";
                    echo "<th style='padding: 8px;'>郵箱</th>";
                    echo "<th style='padding: 8px;'>姓名</th>";
                    echo "<th style='padding: 8px;'>管理員</th>";
                    echo "<th style='padding: 8px;'>活躍</th>";
                    echo "<th style='padding: 8px;'>創建時間</th>";
                    echo "</tr>";
                    
                    while ($row = mysqli_fetch_assoc($users_result)) {
                        echo "<tr>";
                        echo "<td style='padding: 8px;'>" . $row['id'] . "</td>";
                        echo "<td style='padding: 8px;'>" . $row['username'] . "</td>";
                        echo "<td style='padding: 8px;'>" . $row['email'] . "</td>";
                        echo "<td style='padding: 8px;'>" . $row['first_name'] . " " . $row['last_name'] . "</td>";
                        echo "<td style='padding: 8px;'>" . ($row['is_admin'] ? '是' : '否') . "</td>";
                        echo "<td style='padding: 8px;'>" . ($row['is_active'] ? '是' : '否') . "</td>";
                        echo "<td style='padding: 8px;'>" . $row['created_at'] . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } else {
                echo "<p>❌ users 表是空的</p>";
            }
        }
        
    } else {
        echo "<p>❌ users 表不存在</p>";
        
        // Try to create users table
        echo "<h3>嘗試創建 users 表</h3>";
        $create_users = "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50),
            last_name VARCHAR(50),
            nickname VARCHAR(50),
            gender ENUM('male', 'female', 'other', 'prefer_not_to_say') DEFAULT 'prefer_not_to_say',
            birthday DATE,
            language VARCHAR(10) DEFAULT 'en',
            email VARCHAR(100) UNIQUE,
            phone_number VARCHAR(20),
            street_address VARCHAR(255),
            city VARCHAR(100),
            state VARCHAR(50),
            zip_code VARCHAR(10),
            is_verified BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            last_seen TIMESTAMP NULL DEFAULT NULL,
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_is_active (is_active)
        )";
        
        if (mysqli_query($link, $create_users)) {
            echo "<p>✅ users 表創建成功</p>";
        } else {
            echo "<p>❌ users 表創建失敗: " . mysqli_error($link) . "</p>";
        }
    }
    
    // Now try to insert users directly
    echo "<h3>直接插入用戶</h3>";
    
    $password = 'k1e9l9v9in';
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert admin user
    $admin_insert = "INSERT INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin, language) 
                     VALUES ('admin', '$hashed_password', '<EMAIL>', 'System', 'Administrator', 1, 1, 1, 'en')";
    
    if (mysqli_query($link, $admin_insert)) {
        echo "<p>✅ 管理員用戶插入成功</p>";
    } else {
        echo "<p>❌ 管理員用戶插入失敗: " . mysqli_error($link) . "</p>";
    }
    
    // Insert regular user
    $user_insert = "INSERT INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin, language) 
                    VALUES ('KelvinKMS', '$hashed_password', '<EMAIL>', 'Kelvin', 'KMS', 1, 1, 0, 'en')";
    
    if (mysqli_query($link, $user_insert)) {
        echo "<p>✅ 普通用戶插入成功</p>";
    } else {
        echo "<p>❌ 普通用戶插入失敗: " . mysqli_error($link) . "</p>";
    }
    
    // Check again
    echo "<h3>再次檢查用戶表</h3>";
    $final_count = "SELECT COUNT(*) as count FROM users";
    $final_result = mysqli_query($link, $final_count);
    
    if ($final_result) {
        $final_row = mysqli_fetch_assoc($final_result);
        echo "<p>最終記錄數: <strong>" . $final_row['count'] . "</strong></p>";
        
        if ($final_row['count'] > 0) {
            $final_users = "SELECT username, email, is_admin FROM users";
            $final_users_result = mysqli_query($link, $final_users);
            
            echo "<ul>";
            while ($row = mysqli_fetch_assoc($final_users_result)) {
                echo "<li>" . $row['username'] . " (" . $row['email'] . ") - " . ($row['is_admin'] ? '管理員' : '普通用戶') . "</li>";
            }
            echo "</ul>";
        }
    }
    
    mysqli_close($link);
    
} catch (Exception $e) {
    echo "<p>❌ 錯誤: " . $e->getMessage() . "</p>";
}
?>
