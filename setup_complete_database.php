<?php
// Complete database setup
header('Content-Type: text/html; charset=utf-8');

echo "<h2>完整數據庫設置</h2>";

try {
    // Connect to MySQL server (without specifying database)
    $link = mysqli_connect('localhost', 'root', '');
    
    if (!$link) {
        throw new Exception("MySQL connection failed: " . mysqli_connect_error());
    }
    
    echo "<p>✅ MySQL 服務器連接成功</p>";
    
    // Create database if not exists
    $create_db = "CREATE DATABASE IF NOT EXISTS kelvinkms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if (mysqli_query($link, $create_db)) {
        echo "<p>✅ 數據庫 'kelvinkms' 創建/確認成功</p>";
    } else {
        echo "<p>❌ 數據庫創建失敗: " . mysqli_error($link) . "</p>";
    }
    
    // Select database
    if (mysqli_select_db($link, 'kelvinkms')) {
        echo "<p>✅ 已選擇數據庫 'kelvinkms'</p>";
    } else {
        echo "<p>❌ 無法選擇數據庫: " . mysqli_error($link) . "</p>";
    }
    
    // Create users table
    echo "<h3>創建 users 表</h3>";
    $create_users = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(50),
        last_name VARCHAR(50),
        nickname VARCHAR(50),
        gender ENUM('male', 'female', 'other', 'prefer_not_to_say') DEFAULT 'prefer_not_to_say',
        birthday DATE,
        language VARCHAR(10) DEFAULT 'en',
        email VARCHAR(100) UNIQUE,
        phone_number VARCHAR(20),
        street_address VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(50),
        zip_code VARCHAR(10),
        is_verified BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        is_admin BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        last_seen TIMESTAMP NULL DEFAULT NULL,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_is_active (is_active)
    )";
    
    if (mysqli_query($link, $create_users)) {
        echo "<p>✅ users 表創建成功</p>";
    } else {
        echo "<p>❌ users 表創建失敗: " . mysqli_error($link) . "</p>";
    }
    
    // Create user_wallets table
    echo "<h3>創建 user_wallets 表</h3>";
    $create_wallets = "CREATE TABLE IF NOT EXISTS user_wallets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL UNIQUE,
        balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        frozen_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        commission_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total_deposited DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total_spent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total_commissions DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_balance (balance)
    )";
    
    if (mysqli_query($link, $create_wallets)) {
        echo "<p>✅ user_wallets 表創建成功</p>";
    } else {
        echo "<p>❌ user_wallets 表創建失敗: " . mysqli_error($link) . "</p>";
    }
    
    // Create affiliate_codes table
    echo "<h3>創建 affiliate_codes 表</h3>";
    $create_affiliate = "CREATE TABLE IF NOT EXISTS affiliate_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL UNIQUE,
        affiliate_code VARCHAR(20) NOT NULL UNIQUE,
        is_active BOOLEAN DEFAULT TRUE,
        total_referrals INT DEFAULT 0,
        total_commissions DECIMAL(10,2) DEFAULT 0.00,
        total_withdrawn DECIMAL(10,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_affiliate_code (affiliate_code),
        INDEX idx_user_id (user_id),
        INDEX idx_active (is_active)
    )";
    
    if (mysqli_query($link, $create_affiliate)) {
        echo "<p>✅ affiliate_codes 表創建成功</p>";
    } else {
        echo "<p>❌ affiliate_codes 表創建失敗: " . mysqli_error($link) . "</p>";
    }
    
    // Insert users
    echo "<h3>插入用戶數據</h3>";
    $password = password_hash('k1e9l9v9in', PASSWORD_DEFAULT);
    
    // Admin user
    $admin_sql = "INSERT IGNORE INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin, language) 
                 VALUES ('admin', '$password', '<EMAIL>', 'System', 'Administrator', 1, 1, 1, 'en')";
    
    if (mysqli_query($link, $admin_sql)) {
        echo "<p>✅ 管理員用戶插入成功</p>";
        $admin_id = mysqli_insert_id($link);
        if ($admin_id == 0) {
            // Get existing admin ID
            $get_admin = mysqli_query($link, "SELECT id FROM users WHERE username = 'admin'");
            $admin_row = mysqli_fetch_assoc($get_admin);
            $admin_id = $admin_row['id'];
        }
        echo "<p>&nbsp;&nbsp;&nbsp;管理員 ID: $admin_id</p>";
    } else {
        echo "<p>❌ 管理員用戶插入失敗: " . mysqli_error($link) . "</p>";
    }
    
    // Regular user
    $user_sql = "INSERT IGNORE INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin, language) 
                VALUES ('KelvinKMS', '$password', '<EMAIL>', 'Kelvin', 'KMS', 1, 1, 0, 'en')";
    
    if (mysqli_query($link, $user_sql)) {
        echo "<p>✅ 普通用戶插入成功</p>";
        $user_id = mysqli_insert_id($link);
        if ($user_id == 0) {
            // Get existing user ID
            $get_user = mysqli_query($link, "SELECT id FROM users WHERE username = 'KelvinKMS'");
            $user_row = mysqli_fetch_assoc($get_user);
            $user_id = $user_row['id'];
        }
        echo "<p>&nbsp;&nbsp;&nbsp;普通用戶 ID: $user_id</p>";
    } else {
        echo "<p>❌ 普通用戶插入失敗: " . mysqli_error($link) . "</p>";
    }
    
    // Insert wallets
    if (isset($admin_id)) {
        $admin_wallet = "INSERT IGNORE INTO user_wallets (user_id, balance) VALUES ($admin_id, 1000.00)";
        mysqli_query($link, $admin_wallet);
        echo "<p>✅ 管理員錢包創建</p>";
    }
    
    if (isset($user_id)) {
        $user_wallet = "INSERT IGNORE INTO user_wallets (user_id, balance) VALUES ($user_id, 100.00)";
        mysqli_query($link, $user_wallet);
        echo "<p>✅ 普通用戶錢包創建</p>";
    }
    
    // Insert affiliate codes
    if (isset($admin_id)) {
        $admin_affiliate = "INSERT IGNORE INTO affiliate_codes (user_id, affiliate_code) VALUES ($admin_id, 'ADMIN123')";
        mysqli_query($link, $admin_affiliate);
        echo "<p>✅ 管理員推薦代碼創建</p>";
    }
    
    if (isset($user_id)) {
        $user_affiliate = "INSERT IGNORE INTO affiliate_codes (user_id, affiliate_code) VALUES ($user_id, 'KELVIN123')";
        mysqli_query($link, $user_affiliate);
        echo "<p>✅ 普通用戶推薦代碼創建</p>";
    }
    
    // Final check
    echo "<h3>最終檢查</h3>";
    $final_count = mysqli_query($link, "SELECT COUNT(*) as count FROM users");
    $count = mysqli_fetch_assoc($final_count)['count'];
    echo "<p>用戶總數: <strong>$count</strong></p>";
    
    if ($count > 0) {
        $all_users = mysqli_query($link, "SELECT username, email, is_admin FROM users");
        echo "<ul>";
        while ($row = mysqli_fetch_assoc($all_users)) {
            echo "<li><strong>" . $row['username'] . "</strong> (" . $row['email'] . ") - " . ($row['is_admin'] ? '管理員' : '普通用戶') . "</li>";
        }
        echo "</ul>";
    }
    
    mysqli_close($link);
    
    echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ 數據庫設置完成</h3>";
    echo "<p><strong>登錄信息：</strong></p>";
    echo "<p>管理員: admin / k1e9l9v9in</p>";
    echo "<p>普通用戶: KelvinKMS / k1e9l9v9in</p>";
    echo "</div>";
    
    echo "<p><a href='index.php'>測試登錄</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ 錯誤: " . $e->getMessage() . "</p>";
}
?>
