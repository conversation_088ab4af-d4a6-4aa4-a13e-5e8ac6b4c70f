<?php
// Simple PC Tables Setup
require_once 'KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

header('Content-Type: application/json; charset=utf-8');

$local_link = get_db_connection();

if (!$local_link) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$results = [];

// Create PC Component Categories Table
$sql = "CREATE TABLE IF NOT EXISTS pc_component_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    category_name_en VARCHAR(100) NOT NULL,
    category_name_zh VARCHAR(100) NOT NULL,
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
)";

if (mysqli_query($local_link, $sql)) {
    $results[] = "✅ pc_component_categories table created";
} else {
    $results[] = "❌ Failed to create pc_component_categories: " . mysqli_error($local_link);
}

// Insert default categories
$categories = [
    ['CPU', 'CPU', 'CPU', 'Central Processing Unit', 1],
    ['GPU', 'GPU', 'GPU', 'Graphics Processing Unit', 2],
    ['RAM', 'RAM', 'RAM', 'Memory', 3],
    ['Storage', 'Storage', 'Storage', 'Storage Devices', 4],
    ['PSU', 'PSU', 'PSU', 'Power Supply Unit', 5],
    ['Case', 'Case', 'Case', 'Computer Case', 6],
    ['Motherboard', 'Motherboard', 'Motherboard', 'Motherboard', 7]
];

foreach ($categories as $category) {
    $sql = "INSERT IGNORE INTO pc_component_categories (category_name, category_name_en, category_name_zh, description, sort_order) 
            VALUES (?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($local_link, $sql);
    mysqli_stmt_bind_param($stmt, "ssssi", $category[0], $category[1], $category[2], $category[3], $category[4]);
    if (mysqli_stmt_execute($stmt)) {
        $results[] = "✅ Category '{$category[0]}' inserted";
    } else {
        $results[] = "⚠️ Category '{$category[0]}' may already exist";
    }
    mysqli_stmt_close($stmt);
}

// Create PC Components Table
$sql = "CREATE TABLE IF NOT EXISTS pc_components (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    component_name VARCHAR(200) NOT NULL,
    component_name_en VARCHAR(200) NOT NULL,
    component_name_zh VARCHAR(200) NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    specifications JSON,
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    stock_quantity INT DEFAULT 0,
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category_id),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order),
    INDEX idx_price (current_price)
)";

if (mysqli_query($local_link, $sql)) {
    $results[] = "✅ pc_components table created";
} else {
    $results[] = "❌ Failed to create pc_components: " . mysqli_error($local_link);
}

// Create PC Prebuilt Configs Table
$sql = "CREATE TABLE IF NOT EXISTS pc_prebuilt_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_name VARCHAR(200) NOT NULL,
    config_name_en VARCHAR(200) NOT NULL,
    config_name_zh VARCHAR(200) NOT NULL,
    tier ENUM('budget', 'mid', 'high', 'premium') DEFAULT 'mid',
    primary_use ENUM('gaming', 'work', 'content_creation', 'general') DEFAULT 'general',
    description TEXT,
    description_en TEXT,
    description_zh TEXT,
    components JSON NOT NULL COMMENT 'JSON array of component IDs and quantities',
    specifications_summary JSON COMMENT 'Summary of key specifications',
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order),
    INDEX idx_tier (tier),
    INDEX idx_use (primary_use),
    INDEX idx_price (current_price)
)";

if (mysqli_query($local_link, $sql)) {
    $results[] = "✅ pc_prebuilt_configs table created";
} else {
    $results[] = "❌ Failed to create pc_prebuilt_configs: " . mysqli_error($local_link);
}

// Create PC Orders Table
$sql = "CREATE TABLE IF NOT EXISTS pc_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_type ENUM('custom', 'prebuilt') NOT NULL DEFAULT 'custom',
    prebuilt_config_id INT NULL COMMENT 'Reference to prebuilt config if order_type is prebuilt',
    components JSON COMMENT 'Selected components and quantities',
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    shipping_address TEXT,
    notes TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created (created_at)
)";

if (mysqli_query($local_link, $sql)) {
    $results[] = "✅ pc_orders table created";
} else {
    $results[] = "❌ Failed to create pc_orders: " . mysqli_error($local_link);
}

close_db_connection($local_link);

echo json_encode([
    'success' => true,
    'message' => 'PC tables setup completed',
    'results' => $results
]);
?>
