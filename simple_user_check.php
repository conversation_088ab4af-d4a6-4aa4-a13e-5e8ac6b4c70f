<?php
// Simple user check
require_once 'KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

echo "<h2>簡單用戶檢查</h2>";

try {
    $link = get_db_connection();
    echo "<p>✅ 數據庫連接成功</p>";
    
    // Show all tables
    echo "<h3>所有數據表</h3>";
    $tables_result = mysqli_query($link, "SHOW TABLES");
    if ($tables_result) {
        echo "<ul>";
        while ($row = mysqli_fetch_array($tables_result)) {
            echo "<li>" . $row[0] . "</li>";
        }
        echo "</ul>";
    }
    
    // Check users table specifically
    echo "<h3>檢查 users 表</h3>";
    $users_check = mysqli_query($link, "SELECT COUNT(*) as count FROM users");
    if ($users_check) {
        $count = mysqli_fetch_assoc($users_check)['count'];
        echo "<p>users 表記錄數: <strong>$count</strong></p>";
        
        if ($count == 0) {
            echo "<p>❌ users 表是空的，正在創建用戶...</p>";
            
            // Create users directly
            $password = password_hash('k1e9l9v9in', PASSWORD_DEFAULT);
            
            // Admin user
            $admin_sql = "INSERT INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin) 
                         VALUES ('admin', '$password', '<EMAIL>', 'System', 'Administrator', 1, 1, 1)";
            
            if (mysqli_query($link, $admin_sql)) {
                echo "<p>✅ 管理員創建成功</p>";
            } else {
                echo "<p>❌ 管理員創建失敗: " . mysqli_error($link) . "</p>";
            }
            
            // Regular user
            $user_sql = "INSERT INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin) 
                        VALUES ('KelvinKMS', '$password', '<EMAIL>', 'Kelvin', 'KMS', 1, 1, 0)";
            
            if (mysqli_query($link, $user_sql)) {
                echo "<p>✅ 普通用戶創建成功</p>";
            } else {
                echo "<p>❌ 普通用戶創建失敗: " . mysqli_error($link) . "</p>";
            }
            
            // Check again
            $final_check = mysqli_query($link, "SELECT COUNT(*) as count FROM users");
            $final_count = mysqli_fetch_assoc($final_check)['count'];
            echo "<p>創建後記錄數: <strong>$final_count</strong></p>";
        }
        
        // Show all users
        echo "<h3>所有用戶</h3>";
        $all_users = mysqli_query($link, "SELECT * FROM users");
        if ($all_users && mysqli_num_rows($all_users) > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>用戶名</th><th>郵箱</th><th>姓名</th><th>管理員</th><th>活躍</th></tr>";
            while ($user = mysqli_fetch_assoc($all_users)) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td><strong>" . $user['username'] . "</strong></td>";
                echo "<td>" . $user['email'] . "</td>";
                echo "<td>" . $user['first_name'] . " " . $user['last_name'] . "</td>";
                echo "<td>" . ($user['is_admin'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($user['is_active'] ? '✅' : '❌') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ 沒有找到任何用戶</p>";
        }
    } else {
        echo "<p>❌ 無法查詢 users 表: " . mysqli_error($link) . "</p>";
    }
    
    close_db_connection($link);
    
} catch (Exception $e) {
    echo "<p>❌ 錯誤: " . $e->getMessage() . "</p>";
}

echo "<p><a href='index.php'>返回首頁</a></p>";
?>
