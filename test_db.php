<?php
// Test database connection and check tables
require_once 'KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

echo "<h2>Database Connection Test</h2>";

try {
    $local_link = get_db_connection();
    echo "<p>✅ Database connection successful</p>";
    
    // Check if PC tables exist
    $tables = ['pc_component_categories', 'pc_components', 'pc_prebuilt_configs', 'pc_orders'];
    
    foreach ($tables as $table) {
        $result = mysqli_query($local_link, "SHOW TABLES LIKE '$table'");
        if (mysqli_num_rows($result) > 0) {
            echo "<p>✅ Table '$table' exists</p>";
            
            // Count rows
            $count_result = mysqli_query($local_link, "SELECT COUNT(*) as count FROM $table");
            $count = mysqli_fetch_assoc($count_result)['count'];
            echo "<p>&nbsp;&nbsp;&nbsp;→ $count rows</p>";
        } else {
            echo "<p>❌ Table '$table' does not exist</p>";
        }
    }
    
    close_db_connection($local_link);
    
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>API Test</h2>";
echo "<p><a href='KMS_Apps/KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=get_categories' target='_blank'>Test get_categories API</a></p>";
echo "<p><a href='KMS_Apps/KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=get_components' target='_blank'>Test get_components API</a></p>";
echo "<p><a href='KMS_Apps/KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=get_prebuilt_configs' target='_blank'>Test get_prebuilt_configs API</a></p>";
?>
