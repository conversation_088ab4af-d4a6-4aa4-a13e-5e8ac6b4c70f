<?php
// Verify created users
require_once 'KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h2>驗證用戶帳號</h2>";

try {
    $link = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
    
    if (!$link) {
        throw new Exception("Database connection failed: " . mysqli_connect_error());
    }
    
    echo "<p>✅ 數據庫連接成功</p>";
    
    // Check users
    $sql = "SELECT id, username, email, first_name, last_name, is_admin, is_active, is_verified, created_at FROM users WHERE username IN ('admin', 'KelvinKMS')";
    $result = mysqli_query($link, $sql);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<h3>用戶列表：</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>用戶名</th>";
        echo "<th style='padding: 8px;'>郵箱</th>";
        echo "<th style='padding: 8px;'>姓名</th>";
        echo "<th style='padding: 8px;'>管理員</th>";
        echo "<th style='padding: 8px;'>活躍</th>";
        echo "<th style='padding: 8px;'>已驗證</th>";
        echo "<th style='padding: 8px;'>創建時間</th>";
        echo "</tr>";
        
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $row['id'] . "</td>";
            echo "<td style='padding: 8px; font-weight: bold;'>" . $row['username'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['email'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['first_name'] . " " . $row['last_name'] . "</td>";
            echo "<td style='padding: 8px;'>" . ($row['is_admin'] ? '✅ 是' : '❌ 否') . "</td>";
            echo "<td style='padding: 8px;'>" . ($row['is_active'] ? '✅ 是' : '❌ 否') . "</td>";
            echo "<td style='padding: 8px;'>" . ($row['is_verified'] ? '✅ 是' : '❌ 否') . "</td>";
            echo "<td style='padding: 8px;'>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ 沒有找到用戶</p>";
    }
    
    // Check wallets
    echo "<h3>錢包信息：</h3>";
    $wallet_sql = "SELECT uw.*, u.username FROM user_wallets uw 
                   JOIN users u ON uw.user_id = u.id 
                   WHERE u.username IN ('admin', 'KelvinKMS')";
    $wallet_result = mysqli_query($link, $wallet_sql);
    
    if ($wallet_result && mysqli_num_rows($wallet_result) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>用戶名</th>";
        echo "<th style='padding: 8px;'>餘額</th>";
        echo "<th style='padding: 8px;'>凍結餘額</th>";
        echo "<th style='padding: 8px;'>佣金餘額</th>";
        echo "<th style='padding: 8px;'>總存款</th>";
        echo "<th style='padding: 8px;'>總支出</th>";
        echo "</tr>";
        
        while ($row = mysqli_fetch_assoc($wallet_result)) {
            echo "<tr>";
            echo "<td style='padding: 8px; font-weight: bold;'>" . $row['username'] . "</td>";
            echo "<td style='padding: 8px;'>$" . number_format($row['balance'], 2) . "</td>";
            echo "<td style='padding: 8px;'>$" . number_format($row['frozen_balance'], 2) . "</td>";
            echo "<td style='padding: 8px;'>$" . number_format($row['commission_balance'], 2) . "</td>";
            echo "<td style='padding: 8px;'>$" . number_format($row['total_deposited'], 2) . "</td>";
            echo "<td style='padding: 8px;'>$" . number_format($row['total_spent'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ 沒有找到錢包信息</p>";
    }
    
    // Check affiliate codes
    echo "<h3>推薦代碼：</h3>";
    $affiliate_sql = "SELECT ac.*, u.username FROM affiliate_codes ac 
                      JOIN users u ON ac.user_id = u.id 
                      WHERE u.username IN ('admin', 'KelvinKMS')";
    $affiliate_result = mysqli_query($link, $affiliate_sql);
    
    if ($affiliate_result && mysqli_num_rows($affiliate_result) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>用戶名</th>";
        echo "<th style='padding: 8px;'>推薦代碼</th>";
        echo "<th style='padding: 8px;'>活躍</th>";
        echo "<th style='padding: 8px;'>總推薦數</th>";
        echo "<th style='padding: 8px;'>總佣金</th>";
        echo "</tr>";
        
        while ($row = mysqli_fetch_assoc($affiliate_result)) {
            echo "<tr>";
            echo "<td style='padding: 8px; font-weight: bold;'>" . $row['username'] . "</td>";
            echo "<td style='padding: 8px; color: #0066cc; font-weight: bold;'>" . $row['affiliate_code'] . "</td>";
            echo "<td style='padding: 8px;'>" . ($row['is_active'] ? '✅ 是' : '❌ 否') . "</td>";
            echo "<td style='padding: 8px;'>" . $row['total_referrals'] . "</td>";
            echo "<td style='padding: 8px;'>$" . number_format($row['total_commissions'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ 沒有找到推薦代碼</p>";
    }
    
    // Test password verification
    echo "<h3>密碼驗證測試：</h3>";
    $password_test = 'k1e9l9v9in';
    
    $password_sql = "SELECT username, password FROM users WHERE username IN ('admin', 'KelvinKMS')";
    $password_result = mysqli_query($link, $password_sql);
    
    if ($password_result && mysqli_num_rows($password_result) > 0) {
        while ($row = mysqli_fetch_assoc($password_result)) {
            $is_valid = password_verify($password_test, $row['password']);
            echo "<p>" . $row['username'] . " 密碼驗證: " . ($is_valid ? '✅ 正確' : '❌ 錯誤') . "</p>";
        }
    }
    
    mysqli_close($link);
    
    echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ 驗證完成</h3>";
    echo "<p>兩個帳號已成功創建並可以使用以下信息登錄：</p>";
    echo "<p><strong>管理員：</strong> admin / k1e9l9v9in</p>";
    echo "<p><strong>普通會員：</strong> KelvinKMS / k1e9l9v9in</p>";
    echo "</div>";
    
    echo "<p><a href='index.php' style='background: #0066cc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>測試登錄</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ 錯誤: " . $e->getMessage() . "</p>";
}
?>
